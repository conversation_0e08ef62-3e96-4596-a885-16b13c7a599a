<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.genn.boot.nova</groupId>
        <artifactId>genn-spring-boot-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <groupId>cn.genn.app</groupId>
    <artifactId>genn-ai-agent-orcha</artifactId>
    <version>${revision}</version>
    <description>genn ai agent编排层</description>
    <packaging>jar</packaging>


    <properties>
        <skipTests>true</skipTests>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <jakarta.servlet-api.version>5.0.0</jakarta.servlet-api.version>
    </properties>

    <dependencies>
        <!-- ==================== genn 内部依赖   ===================      -->

        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-database</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-skywalking</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-event-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-monitor</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-bridge</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-ai</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-lock</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-upm</artifactId>
        </dependency>
        <!-- 响应式Redis依赖，用于Pub/Sub功能 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis-reactive</artifactId>
        </dependency>
        <!-- ======================= 服务api依赖  ===========================      -->
        <!-- 其他项目 api依赖       -->

        <!-- ======================= 其他三方依赖   =======================      -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>


        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
            <version>${jakarta.servlet-api.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

    </dependencies>

    <build>
        <finalName>genn-ai-agent-orcha</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
