FROM genn-repository-cn-beijing.cr.volces.com/genn-ops/base/openjdk21:v1
USER root

WORKDIR /appdata

COPY target/genn-ai-agent-orcha.jar ./

CMD java -javaagent:/appdata/skywalking-agent/skywalking-agent.jar \
    -DSW_AGENT_NAME=${SKYWALKING_AGENT_NAME} \
    -DSW_AGENT_COLLECTOR_BACKEND_SERVICES=${SKYWALKING_ADDRESS} \
    -Xms512m \
    -Xmx1024m \
    -server \
    -XX:+UseZGC \
    -XX:+HeapDumpOnOutOfMemoryError \
    -XX:HeapDumpPath=./heap-dump.hprof \
    -Xlog:gc*:file=./gc.log:time,uptime,level,tags:filecount=10,filesize=100M \
    -Djava.awt.headless=true \
    -XX:ConcGCThreads=2 \
    -Duser.timezone=Asia/Shanghai \
    -XX:ZUncommitDelay=30 \
    -XX:+UseCompressedOops  \
    -XX:+UseStringDeduplication \
    -XX:+ExitOnOutOfMemoryError \
    -Dspring.cloud.nacos.config.server-addr=${NACOS_ADDRESS} \
    -Dspring.cloud.nacos.config.namespace=${NACOS_NAMESPACE} \
    -Dserver.port=8080 -jar genn-ai-agent-orcha.jar
