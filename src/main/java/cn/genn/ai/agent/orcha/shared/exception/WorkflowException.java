package cn.genn.ai.agent.orcha.shared.exception;

import cn.genn.core.exception.BaseException;
import cn.genn.core.exception.MessageCodeWrap;

/**
 * <AUTHOR>
 */
public class WorkflowException extends BaseException {

    public WorkflowException() {
        super(AgentOrchaMessageCode.FAIL);
    }
    public WorkflowException(String message) {
        super(AgentOrchaMessageCode.FAIL.buildCode(), message);
    }

    public WorkflowException(String code, String message) {
        super(code, message);
    }

    public WorkflowException(String code, String message, Throwable throwable) {
        super(code, message, throwable);
    }

    public WorkflowException(MessageCodeWrap messageCode, Object... args) {
        super(messageCode, args);
    }
}
