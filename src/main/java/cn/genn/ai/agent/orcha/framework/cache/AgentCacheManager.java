package cn.genn.ai.agent.orcha.framework.cache;

import cn.genn.ai.agent.orcha.shared.constants.CacheConstants;
import cn.genn.ai.agent.orcha.shared.enums.TaskStatus;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class AgentCacheManager {

    private final StringRedisTemplate stringRedisTemplate;

    public TaskStatus getTaskStatus(String taskId) {
        String key = CacheConstants.buildAgentTaskStatusKey(taskId);
        String statusStr = stringRedisTemplate.opsForValue().get(key);
        return statusStr == null ? TaskStatus.UNKNOWN : TaskStatus.fromCode(statusStr);
    }

}
