package cn.genn.ai.agent.orcha.framework.persistence.service;

import cn.genn.ai.agent.orcha.api.dto.AgentMessage;
import cn.genn.ai.agent.orcha.framework.cache.RedisStreamManager;
import cn.genn.ai.agent.orcha.framework.persistence.converter.AgentMessageConverter;
import cn.genn.ai.agent.orcha.framework.persistence.entity.AgentChatMessage;
import cn.genn.ai.agent.orcha.framework.persistence.entity.AgentChatSession;
import cn.genn.ai.agent.orcha.framework.persistence.event.MessagePersistenceEvent;
import cn.genn.ai.agent.orcha.framework.persistence.mapper.AgentChatMessageMapper;
import cn.genn.ai.agent.orcha.framework.persistence.mapper.AgentChatSessionMapper;
import cn.genn.ai.agent.orcha.framework.persistence.utils.message.MergedMessage;
import cn.genn.ai.agent.orcha.framework.persistence.utils.message.MessageMergeStrategy;
import cn.genn.ai.agent.orcha.shared.enums.CardType;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.stream.MapRecord;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 消息持久化服务
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MessagePersistenceService {

    private final RedisStreamManager redisStreamManager;
    private final AgentChatSessionMapper sessionMapper;
    private final AgentChatMessageMapper messageMapper;
    private final AgentMessageConverter agentMessageConverter;


    /**
     * 处理消息入库
     *
     * @param event 消息入库事件
     */
    @Transactional(rollbackFor = Exception.class)
    public void processMessagePersistence(MessagePersistenceEvent event) {
        try {
            log.info("开始处理消息入库，taskId: {}, chatId: {}, operationType: {}",
                event.getTaskId(), event.getChatId(), event.getOperationType());

            // 1. 确保会话记录存在
            ensureChatSessionExists(event);

            if (event.getOperationType() == MessagePersistenceEvent.OperationType.CREATE) {
                // 创建初始消息记录
                createInitialChatMessage(event);
            } else {
                // 更新消息记录
                updateChatMessage(event);
            }

            log.info("消息入库完成，taskId: {}, operationType: {}",
                event.getTaskId(), event.getOperationType());

        } catch (Exception e) {
            log.error("消息入库失败，taskId: {}, operationType: {}",
                event.getTaskId(), event.getOperationType(), e);
            throw e;
        }
    }

    public List<MergedMessage> buildMergeMessages(List<MapRecord<String, Object, Object>> records) {
        // 3. 转换为AgentMessage并过滤
        List<AgentMessage> messages = records.stream()
            .map(agentMessageConverter::mapRecordToAgentMessage)
            .filter(Objects::nonNull)
            .filter(this::isValidMessage)
            .collect(Collectors.toList());

        // 4. 合并消息
        return mergeMessages(messages);
    }

    /**
     * 创建初始聊天消息记录
     */
    private void createInitialChatMessage(MessagePersistenceEvent event) {
        AgentChatMessage chatMessage = agentMessageConverter.eventToChatMessage(event);
        chatMessage.setMessageContent(new ArrayList<>()); // 初始为空列表
        messageMapper.insert(chatMessage);
        log.info("创建初始聊天消息记录，chatId: {}, taskId: {}, taskStatus: {}",
            event.getChatId(), event.getTaskId(), event.getTaskStatus());
    }

    /**
     * 更新聊天消息记录
     */
    private void updateChatMessage(MessagePersistenceEvent event) {
        LambdaQueryWrapper<AgentChatMessage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentChatMessage::getTaskId, event.getTaskId())
            .orderByDesc(AgentChatMessage::getCreateTime)
            .last("LIMIT 2");
        List<AgentChatMessage> existingMessages = messageMapper.selectList(queryWrapper);
        AgentChatMessage topChatMessage = existingMessages.getLast();
        String lastWriteMessageId = topChatMessage.getLastMessageId();
        if (CharSequenceUtil.isEmpty(lastWriteMessageId)) {
            lastWriteMessageId = "0-0"; // 如果没有lastMessageId，使用默认值
        }

        // 从Redis Stream读取消息
        List<MapRecord<String, Object, Object>> records = redisStreamManager.readMessages(event.getTaskId(), lastWriteMessageId, 0);
        if (records.isEmpty()) {
            log.warn("未找到任务消息，taskId: {}", event.getTaskId());
            // 即使没有消息，也要更新任务状态
            updateTaskStatusOnly(event);
            return;
        }

        // 合并消息
        List<MergedMessage> mergedMessages = buildMergeMessages(records);
        String lastMessageId = mergedMessages.isEmpty() ? null : mergedMessages.getLast().getLastMessageId();

        AgentChatMessage existingMessage = existingMessages.getFirst();
        if (existingMessage != null) {
            existingMessage.setLastMessageId(lastMessageId);
            existingMessage.setMessageContent(mergedMessages)
                .setTaskStatus(event.getTaskStatus());
            messageMapper.updateById(existingMessage);
            log.info("更新聊天消息记录，chatId: {}, taskId: {}, taskStatus: {}, 消息数: {}",
                event.getChatId(), event.getTaskId(), event.getTaskStatus(), mergedMessages.size());
        } else {
            log.warn("未找到要更新的消息记录，taskId: {}", event.getTaskId());
            // 如果找不到记录，创建一个新的
            saveChatMessage(event, lastMessageId, mergedMessages);
        }
    }

    /**
     * 仅更新任务状态
     */
    private void updateTaskStatusOnly(MessagePersistenceEvent event) {
        LambdaQueryWrapper<AgentChatMessage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentChatMessage::getTaskId, event.getTaskId())
            .orderByDesc(AgentChatMessage::getCreateTime)
            .last("LIMIT 1");

        AgentChatMessage existingMessage = messageMapper.selectOne(queryWrapper);
        if (existingMessage != null) {
            existingMessage.setTaskStatus(event.getTaskStatus());
            messageMapper.updateById(existingMessage);
            log.info("仅更新任务状态，taskId: {}, taskStatus: {}",
                event.getTaskId(), event.getTaskStatus());
        }
    }

    /**
     * 确保聊天会话记录存在
     */
    private void ensureChatSessionExists(MessagePersistenceEvent event) {
        LambdaQueryWrapper<AgentChatSession> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentChatSession::getChatId, event.getChatId());

        AgentChatSession existingSession = sessionMapper.selectOne(queryWrapper);
        if (existingSession == null) {
            AgentChatSession session = new AgentChatSession()
                .setChatId(event.getChatId())
                .setAppId(event.getAppId())
                .setTitle(StrUtil.isNotBlank(event.getSessionTitle()) ? event.getSessionTitle() : "新对话")
                .setChatMode(event.getChatMode())
                .setExtraConfig(event.getExtraConfig())
                .setTenantId(event.getTenantId())
                .setCreateUserId(event.getUserId())
                .setCreateUserName(event.getUserName());

            sessionMapper.insert(session);
            log.info("创建聊天会话记录，chatId: {}", event.getChatId());
        }
    }


    /**
     * 验证消息是否有效
     * 过滤掉FINISH类型的消息和没有cardType的消息
     */
    private boolean isValidMessage(AgentMessage message) {
        if (message == null || message.getCardType() == null) {
            return false;
        }
        // 过滤掉PING心跳消息
        if (message.getCardType() == CardType.PING) {
            return false;
        }

        return true;
    }

    /**
     * 合并消息 - 按顺序处理，只合并连续的相同类型消息
     */
    private List<MergedMessage> mergeMessages(List<AgentMessage> messages) {
        List<MergedMessage> result = new ArrayList<>();

        if (messages.isEmpty()) {
            return result;
        }

        int i = 0;
        while (i < messages.size()) {
            AgentMessage currentMessage = messages.get(i);
            CardType currentType = currentMessage.getCardType();
            if(!currentType.isStore()){
                i++;
                continue;
            }

            // 收集连续的相同类型消息
            List<AgentMessage> consecutiveMessages = new ArrayList<>();
            consecutiveMessages.add(currentMessage);

            // 向后查找连续的相同类型消息
            int j = i + 1;
            while (j < messages.size() && messages.get(j).getCardType() == currentType) {
                consecutiveMessages.add(messages.get(j));
                j++;
            }

            Class<? extends MessageMergeStrategy> mergeStrategyClass = currentType.getMergeStrategyClass();
            MessageMergeStrategy mergeStrategy = SpringUtil.getBean(mergeStrategyClass);
            List<MergedMessage> mergedMessage = mergeStrategy.mergeMessages(currentType, consecutiveMessages);
            if (mergedMessage != null) {
                result.addAll(mergedMessage);
            }

            // 移动到下一批消息
            i = j;
        }

        return result;
    }


    /**
     * 保存聊天消息到数据库
     */
    private void saveChatMessage(MessagePersistenceEvent event, String lastMessageId, List<MergedMessage> mergedMessages) {
        AgentChatMessage chatMessage = agentMessageConverter.eventToChatMessage(event);
        chatMessage.setMessageContent(mergedMessages);
        chatMessage.setLastMessageId(lastMessageId);
        messageMapper.insert(chatMessage);
        log.info("保存聊天消息到数据库，chatId: {}, taskId: {}", event.getChatId(), event.getTaskId());
    }
}
