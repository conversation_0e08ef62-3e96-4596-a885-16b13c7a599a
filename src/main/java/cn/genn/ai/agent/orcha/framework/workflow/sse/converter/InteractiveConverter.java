package cn.genn.ai.agent.orcha.framework.workflow.sse.converter;

import cn.genn.ai.agent.orcha.api.dto.AgentMessage;
import cn.genn.ai.agent.orcha.api.dto.ExecutionRequest;
import cn.genn.ai.agent.orcha.api.dto.message.InteractiveMessage;
import cn.genn.ai.agent.orcha.framework.workflow.sse.CerebroEventType;
import cn.genn.ai.agent.orcha.shared.enums.CardType;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 交互节点事件转换器
 *
 * 负责处理交互式节点事件，支持用户选择(userSelect)和用户输入(userInput)两种类型
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class InteractiveConverter extends BaseEventConverter {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    @Override
    public boolean supports(String eventType) {
        return CerebroEventType.INTERACTIVE.getCode().equals(eventType);
    }

    @Override
    public List<AgentMessage> convert(ServerSentEvent<String> event, ExecutionRequest request, String taskId) {
        try {
            String data = event.data();
            if (StrUtil.isBlank(data)) {
                return null;
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> dataMap = OBJECT_MAPPER.readValue(data, Map.class);

            @SuppressWarnings("unchecked")
            Map<String, Object> interactive = (Map<String, Object>) dataMap.get("interactive");
            if (interactive == null) {
                log.warn("交互式事件数据格式错误，缺少interactive字段: {}", data);
                return null;
            }

            String interactiveType = (String) interactive.get("type");
            if (StrUtil.isBlank(interactiveType)) {
                log.warn("交互式事件缺少type字段: {}", data);
                return null;
            }

            // 创建交互式消息
            InteractiveMessage interactiveMessage = new InteractiveMessage()
                    .setType(interactiveType)
                    .setParams(extractParams(interactive))
                    .setEntryNodeIds(extractEntryNodeIds(interactive))
                    .setMemoryEdges(extractMemoryEdges(interactive))
                    .setNodeOutputs(extractNodeOutputs(interactive))
                    .setStatus("waiting")
                    .setFinished(false);

            // 构建AgentMessage
            AgentMessage agentMessage = new AgentMessage()
                .setCardType(CardType.INTERACTIVE)
                .setTaskId(taskId)
                .setCardId("interactive_" + System.currentTimeMillis())
                .setStatus(0) // 成功状态
                .setEditable(true) // 交互式节点允许用户编辑
                .setStarted(System.currentTimeMillis() / 1000)
                .setMessages(List.of(interactiveMessage));
            return Collections.singletonList(handleArea(agentMessage, request, taskId));

        } catch (Exception e) {
            log.error("转换交互式事件失败: {}", event.data(), e);
            return null;
        }
    }

    @Override
    public int getOrder() {
        return 50; // 中等优先级
    }

    /**
     * 提取交互参数
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> extractParams(Map<String, Object> interactive) {
        return (Map<String, Object>) interactive.get("params");
    }

    /**
     * 提取入口节点ID列表
     */
    @SuppressWarnings("unchecked")
    private List<String> extractEntryNodeIds(Map<String, Object> interactive) {
        Object entryNodeIds = interactive.get("entryNodeIds");
        if (entryNodeIds instanceof List) {
            return (List<String>) entryNodeIds;
        }
        return new ArrayList<>();
    }

    /**
     * 提取内存边缘信息
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> extractMemoryEdges(Map<String, Object> interactive) {
        Object memoryEdges = interactive.get("memoryEdges");
        if (memoryEdges instanceof List) {
            return (List<Map<String, Object>>) memoryEdges;
        }
        return new ArrayList<>();
    }

    /**
     * 提取节点输出信息
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> extractNodeOutputs(Map<String, Object> interactive) {
        Object nodeOutputs = interactive.get("nodeOutputs");
        if (nodeOutputs instanceof List) {
            return (List<Map<String, Object>>) nodeOutputs;
        }
        return new ArrayList<>();
    }
}
