package cn.genn.ai.agent.orcha.framework.workflow.dto;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Map;

/**
 * Content
 */
@Data
@Builder
@Slf4j
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@AllArgsConstructor
public class Content {
    /**
     * 符合OpenAI标准的类型定义
     * @see <a href="https://platform.openai.com/docs/guides/vision">Vision Guide</a>
     */
    private String type;
    private String text;

    @JsonProperty("image_url")
    private ImageUrl imageUrl;

    private Map<String, Object> rawData;

    public interface Type {
        String TEXT = "text";
        String IMAGE_URL = "image_url"; // 唯一支持的媒体类型
    }

    public static Content ofText(String text) {
        Content content = new Content();
        content.type = Type.TEXT;
        content.text = text;
        return content;
    }

    public static Content ofImage(String urlOrDataURI) {
        Content content = new Content();
        content.type = Type.IMAGE_URL;
        content.imageUrl = new ImageUrl(urlOrDataURI);
        return content;
    }

    public static Content ofImageFile(File file) throws IOException {
        String mimeType = FileUtil.getMimeType(file.getPath());
        byte[] data = Files.readAllBytes(file.toPath());
        return ofImageBytes(data, mimeType);
    }

    public static Content ofImageBytes(byte[] data, String mimeType) {
        String base64 = Base64.getEncoder().encodeToString(data);
        String dataURI = "data:" + mimeType + ";base64," + base64;
        return ofImage(dataURI);
    }

    public static Content ofImageStream(InputStream stream, String mimeType) throws IOException {
        try (InputStream is = stream) {
            return ofImageBytes(IoUtil.readBytes(is), mimeType);
        }
    }

    public static List<Content> textWithImages(String text, List<String> imageSources) {
        List<Content> contents = new ArrayList<>();
        contents.add(ofText(text));
        imageSources.forEach(url -> contents.add(ofImage(url)));
        return contents;
    }

    public static List<Content> textWithImageFiles(String text, List<File> imageFiles) {
        List<Content> contents = new ArrayList<>();
        contents.add(ofText(text));
        imageFiles.forEach(file -> {
            try {
                contents.add(ofImageFile(file));
            } catch (IOException e) {
                log.error("Failed to read image file: {}", file, e);
            }
        });
        return contents;
    }

    @Getter
    public static class ImageUrl {
        // 保持与API字段名一致
        private String url;

        public ImageUrl(String url) {
            this.url = url;
        }

    }
}
