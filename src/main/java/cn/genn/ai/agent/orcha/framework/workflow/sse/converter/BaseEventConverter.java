package cn.genn.ai.agent.orcha.framework.workflow.sse.converter;

import cn.genn.ai.agent.orcha.api.dto.AgentMessage;
import cn.genn.ai.agent.orcha.api.dto.ExecutionRequest;
import cn.genn.ai.agent.orcha.framework.workflow.sse.converter.xml.XmlTagBuffer;
import cn.genn.ai.agent.orcha.framework.workflow.sse.converter.xml.XmlTagBufferManager;
import cn.genn.ai.agent.orcha.shared.constants.CacheConstants;
import cn.genn.ai.agent.orcha.shared.enums.AreaType;
import cn.genn.ai.agent.orcha.shared.enums.CardType;
import cn.genn.ai.agent.orcha.shared.enums.ChatMode;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;

import java.time.Duration;
import java.util.Objects;
import java.util.Optional;

public abstract class BaseEventConverter implements EventConverter{

    /**
     * 全局处理区域等数据
     * @param agentMessage
     * @param request
     * @param taskId
     * @return
     */
    public AgentMessage handleArea(AgentMessage agentMessage, ExecutionRequest request, String taskId) {
        if (ObjUtil.isNull(agentMessage)) {
            return agentMessage;
        }
        XmlTagBuffer tagBuffer = XmlTagBufferManager.getBufferIfPresent(taskId);
        if (Objects.isNull(tagBuffer)) {
            agentMessage.setArea(AreaType.CONTENT);
            return agentMessage;
        }
        XmlTagBuffer.TagState currentTag = tagBuffer.getCurrentTag();
        //1.parentCardId处理
        String parentCardId = Optional.ofNullable(currentTag).map(XmlTagBuffer.TagState::getCardId).orElse("");
        String cardId = agentMessage.getCardId();
        //如果当前标签的id和缓存标签id相同,表示当前当前返回的就是此标签,parentCardId需要取上一级卡片;
        if (StrUtil.isNotEmpty(cardId) && parentCardId.equals(cardId)) {
            parentCardId = Optional.ofNullable(tagBuffer.getParentTag()).map(XmlTagBuffer.TagState::getCardId).orElse("");
        }
        //2.area处理
        AreaType area = Optional.ofNullable(currentTag).map(XmlTagBuffer.TagState::getArea).orElse(AreaType.CONTENT);
        if(agentMessage.getCardType().equals(CardType.THINKING)){
            area = AreaType.THINK;
        }
        //工作空间区域特殊处理
        ChatMode chatMode = request.getChatMode();
        if (chatMode.equals(ChatMode.DEEP_RESEARCH)) {
            if (XmlTagBufferManager.getWorkspaceAreaSign(taskId)) {
                area = AreaType.WORKSPACE;
                //进度处理
                agentMessage.setProgress(tagBuffer.getProgress());
            } else if (tagBuffer.getProgress() != 100.0) {  //非工作空间的第一条节点,进度置为100;除此之外的非工作空间节点,不显示进度
                agentMessage.setProgress(100.0);
                tagBuffer.setProgress(100.0);
            }
        }
        agentMessage.setArea(area);
        agentMessage.setParentCardId(parentCardId);
        return agentMessage;
    }
}
