package cn.genn.ai.agent.orcha.api.dto;

import cn.genn.ai.agent.orcha.shared.enums.AreaType;
import cn.genn.ai.agent.orcha.shared.enums.CardType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * Agent 消息体
 *
 * 统一的Agent对话消息结构
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Agent消息体")
public class AgentMessage {

    @Schema(description = "消息类型标识", example = "assistant")
    private CardType cardType;

    @Schema(description = "当前会话唯一ID", example = "8519337674576314658")
    private String chatId;

    @Schema(description = "当前任务唯一ID", example = "7519337674576314658")
    private String taskId;

    @Schema(description = "当前消息片段ID", example = "card_123")
    private String cardId;

    @Schema(description = "消息在流中的唯一ID，由Redis生成", example = "1720786800000-0")
    private String messageId;

    @Schema(description = "标签card状态（0:完成，1:进行中）", example = "0")
    private Integer status;

    @Schema(description = "是否允许用户编辑", example = "false")
    private Boolean editable = false;

    @Schema(description = "Unix启动时间戳", example = "1750648313")
    private Long started;

    @Schema(description = "消息主体，结构根据cardType变化")
    private List<Object> messages;

    @Schema(description = "区域类型标识")
    private AreaType area = AreaType.CONTENT;

    @Schema(description = "进度条0-100")
    private Double progress;

    @Schema(description = "上级消息片段ID")
    private String parentCardId = "";



}
