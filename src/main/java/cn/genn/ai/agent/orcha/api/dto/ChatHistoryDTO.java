package cn.genn.ai.agent.orcha.api.dto;

import cn.genn.ai.agent.orcha.framework.persistence.utils.message.MergedMessage;
import cn.genn.ai.agent.orcha.shared.enums.TaskStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 聊天历史记录DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
@Schema(description = "聊天历史记录")
public class ChatHistoryDTO {

    @Schema(description = "任务ID")
    private String taskId;

    @Schema(description = "用户输入问题")
    private String question;

    @Schema(description = "文件信息")
    private List<FileInfo> fileInfo;

    @Schema(description = "AI回答内容")
    private List<MergedMessage> answer;

    @Schema(description = "最后一条消息的ID")
    private String lastMessageId;

    @Schema(description = "任务状态")
    private TaskStatus taskStatus;

    @Schema(description = "提问时间")
    private LocalDateTime questionTime;

    @Schema(description = "回答时间")
    private LocalDateTime answerTime;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}
