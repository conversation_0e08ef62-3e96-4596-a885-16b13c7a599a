package cn.genn.ai.agent.orcha.shared.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 执行模式枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum ExecutionMode {

    /**
     * 通用 Agent 模式 - 使用 ReAct 等策略
     */
    AGENT("agent", "通用Agent模式"),

    /**
     * 直连工作流模式 - 直接调用 Cerebro 工作流
     */
    WORKFLOW("workflow", "直连工作流模式");

    /**
     * 模式代码
     */
    @JsonValue
    @EnumValue
    private final String code;

    /**
     * 模式描述
     */
    private final String description;

    /**
     * 根据代码获取执行模式
     */
    public static ExecutionMode fromCode(String code) {
        for (ExecutionMode mode : values()) {
            if (mode.getCode().equals(code)) {
                return mode;
            }
        }
        throw new IllegalArgumentException("未知的执行模式: " + code);
    }
}
