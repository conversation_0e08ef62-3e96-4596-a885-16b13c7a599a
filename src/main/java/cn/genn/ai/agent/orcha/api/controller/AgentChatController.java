package cn.genn.ai.agent.orcha.api.controller;

import cn.genn.ai.agent.orcha.api.dto.*;
import cn.genn.ai.agent.orcha.api.dto.message.TextMessage;
import cn.genn.ai.agent.orcha.api.query.ChatSessionQuery;
import cn.genn.ai.agent.orcha.biz.chat.AgentMessageManager;
import cn.genn.ai.agent.orcha.framework.execution.ExecutionEngine;
import cn.genn.ai.agent.orcha.framework.persistence.entity.AgentChatMessage;
import cn.genn.ai.agent.orcha.framework.persistence.utils.message.MergedMessage;
import cn.genn.ai.agent.orcha.shared.enums.CardType;
import cn.genn.ai.agent.orcha.shared.enums.TaskStatus;
import cn.genn.ai.agent.orcha.shared.exception.AgentOrchaMessageCode;
import cn.genn.ai.agent.orcha.shared.third.GennAIHubClient;
import cn.genn.core.exception.BaseException;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.genn.spring.boot.starter.upm.component.MagicTokenContext;
import cn.genn.web.spring.annotation.ResponseResultWrapper;
import cn.genn.web.spring.utils.ServletUtils;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * Agent 对话控制器
 * <p>
 * 提供基于流式协议的统一AI Agent交互接口
 * 支持多种执行模式（Agent、工作流等）的流式对话和管理
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/chat")
@RequiredArgsConstructor
@Validated
@Tag(name = "AI Agent对话", description = "AI Agent流式对话交互接口")
public class AgentChatController {

    private final List<ExecutionEngine> executionEngines;
    private final AgentMessageManager messageManager;
    private final GennAIHubClient gennAIHubClient;

    @PostMapping(value = "/completions", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "Agent流式对话", description = "AI Agent统一流式对话接口，支持多种执行模式")
    @ResponseResultWrapper(ignore = true)
    public Flux<ServerSentEvent<AgentMessage>> chat(@Valid @RequestBody ExecutionRequest request, HttpServletRequest httpRequest,@RequestHeader("magic-token") String magicToken) {
        log.info("收到Agent流式对话请求，模式: {}, 输入: {}", request.getMode(), request.getInput());
        if(StrUtil.isNotBlank(magicToken)){
            MagicTokenContext.setTokenData(magicToken);
        }
        fillDefaultValues(request, httpRequest);
        validRequest(request);
        ExecutionEngine engine = findSupportedEngine(request);

        return engine.executeStream(request)
            .map(message -> ServerSentEvent.<AgentMessage>builder()
                .id(message.getTaskId() + ":" + message.getMessageId())
                .event("message")
                .data(message)
                .build())
            .doOnError(error -> {
                if (error instanceof IOException) {
                    log.info("客户端连接已断开, TaskId: {}. Reason: {}", request.getTaskId(), error.getMessage());
                } else {
                    log.error("Agent流式对话执行失败", error);
                }
            })
            .onErrorResume(error -> {
                if (error instanceof IOException) {
                    return Flux.empty(); // 如果是IO异常，静默处理，不返回错误消息
                }
                return Flux.just(ServerSentEvent.<AgentMessage>builder()
                    .id(request.getTaskId() + "-error-" + System.currentTimeMillis())
                    .event("error")
                    .data(createErrorMessage(request, "执行失败: " + error.getMessage()))
                    .build());
            });
    }

    @PostMapping("/stop/{chatId}")
    @Operation(summary = "停止执行", description = "停止正在执行的任务")
    public boolean stopExecution(
        @Parameter(description = "聊天ID") @PathVariable String chatId) {
        log.info("收到停止执行请求，chatId: {}", chatId);

        // 尝试所有引擎停止执行
        return executionEngines.stream()
            .anyMatch(engine -> engine.stopExecution(chatId));
    }

    @GetMapping("/history/{chatId}")
    @Operation(summary = "获取聊天记录", description = "根据chatId查询聊天记录，返回一问一答的形式")
    public List<ChatHistoryDTO> getChatHistory(
        @Parameter(description = "聊天ID") @PathVariable String chatId) {
        log.info("获取聊天记录，chatId: {}", chatId);
        return messageManager.getChatHistory(chatId);
    }


    @PostMapping("/session/page")
    @Operation(summary = "分页获取聊天会话列表", description = "分页获取当前用户的聊天会话列表")
    public PageResultDTO<ChatSessionDTO> getChatSessions(@RequestBody ChatSessionQuery query) {
        return messageManager.getChatSessions(query);
    }


    @PostMapping("/session/delete/{chatId}")
    @Operation(summary = "删除指定会话", description = "删除指定会话")
    public void deleteChatSession(@Parameter(description = "聊天ID") @PathVariable String chatId) {
        messageManager.deleteChatSession(chatId);
    }

    @PostMapping("/session/pin/{chatId}")
    @Operation(summary = "置顶会话", description = "将指定会话置顶")
    public void pinChatSession(@Parameter(description = "聊天ID") @PathVariable String chatId) {
        messageManager.pinChatSession(chatId, true);
    }

    @PostMapping("/session/unpin/{chatId}")
    @Operation(summary = "取消置顶会话", description = "取消指定会话的置顶状态")
    public void unpinChatSession(@Parameter(description = "聊天ID") @PathVariable String chatId) {
        messageManager.pinChatSession(chatId, false);
    }

    @PostMapping("/session/updateTitle")
    @Operation(summary = "修改会话标题", description = "修改指定会话的标题")
    public void updateChatSessionTitle(
        @Valid @RequestBody UpdateChatSessionTitleRequest request) {
        messageManager.updateChatSessionTitle(request.getChatId(), request.getTitle());
    }

    /**
     * 查找支持的执行引擎
     */
    private ExecutionEngine findSupportedEngine(ExecutionRequest request) {
        return executionEngines.stream()
            .filter(engine -> engine.supports(request))
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("不支持的执行模式: " + request.getMode()));
    }

    /**
     * 创建错误消息
     */
    private AgentMessage createErrorMessage(ExecutionRequest request, String message) {
        return new AgentMessage()
            .setCardType(CardType.ERROR)
            .setMessages(List.of(new TextMessage().setText(message)))
            .setTaskId(request.getTaskId())
            .setStatus(0);
    }


    private void fillDefaultValues(ExecutionRequest request, HttpServletRequest httpRequest) {
        request.setUserInfo(CurrentUserHolder.getCurrentUser());
        request.setHttpHeaders(ServletUtils.getHeaderMap(httpRequest));
        if (CharSequenceUtil.isEmpty(request.getFormattedInput())) {
            request.setFormattedInput(request.getInput());
        }
        Map<String, String> agentConfig = gennAIHubClient.getGoVowAgentConfig(request.getUserInfo().getToken());
        request.setAppId(agentConfig.get(request.getChatMode().getCode()));
        String lastEventId = request.getLastEventId();
        if (lastEventId == null) {
            lastEventId = httpRequest.getHeader("Last-Event-ID");
        }
        if (lastEventId != null && lastEventId.contains(":")) {
            request.setLastEventId(lastEventId.split(":")[1]);
            request.setTaskId(lastEventId.split(":")[0]);
        }
    }


    private void validRequest(@Valid ExecutionRequest request) {
        //传递了taskId,未传递lastEventId,则需要检查任务状态
        if (CharSequenceUtil.isNotEmpty(request.getTaskId()) && CharSequenceUtil.isEmpty(request.getLastEventId())) {
            TaskStatus taskStatus = messageManager.getTaskStatus(request.getTaskId());
            if (taskStatus == TaskStatus.RUNNING) {
                throw new BaseException(AgentOrchaMessageCode.TASK_ALREADY_RUNNING);
            }
        }else if (CharSequenceUtil.isEmpty(request.getTaskId())) {
            //未传递taskId,则需要检查当前会话状态
            AgentChatMessage agentChatMessage = messageManager.getLastedMessage(request.getChatId());
            if (agentChatMessage != null && agentChatMessage.getTaskStatus() == TaskStatus.RUNNING) {
                throw new BaseException(AgentOrchaMessageCode.CHAT_ALREADY_RUNNING);
            }
            if (agentChatMessage != null) {
                List<MergedMessage> messageContent = agentChatMessage.getMessageContent();
                //从后向前遍历, 如果上次的任务最后的消息是交互式卡片，使用上次的taskId
                if (messageContent != null) {
                    for (int i = messageContent.size() - 1; i >= 0; i--) {
                        MergedMessage message = messageContent.get(i);
                        if (message.getCardType() == CardType.INTERACTIVE) {
                            request.setTaskId(agentChatMessage.getTaskId());
                            break;
                        }
                    }
                }
            }
        }
    }
}
