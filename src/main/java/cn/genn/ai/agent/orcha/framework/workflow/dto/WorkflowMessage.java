package cn.genn.ai.agent.orcha.framework.workflow.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Cerebro消息实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
public class WorkflowMessage {

    /**
     * 数据ID
     */
    private String dataId;

    /**
     * 角色
     */
    private String role;

    /**
     * 是否在UI中隐藏
     */
    private Boolean hideInUI = false;

    /**
     * 消息内容
     */
    private Object content;
}
