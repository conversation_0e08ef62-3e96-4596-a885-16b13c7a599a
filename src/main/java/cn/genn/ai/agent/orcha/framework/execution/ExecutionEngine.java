package cn.genn.ai.agent.orcha.framework.execution;

import cn.genn.ai.agent.orcha.api.dto.AgentMessage;
import cn.genn.ai.agent.orcha.api.dto.ExecutionRequest;
import reactor.core.publisher.Flux;

/**
 * 执行引擎接口
 *
 * 定义统一的执行引擎规范，支持不同的执行模式
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ExecutionEngine {

    /**
     * 流式执行
     *
     * @param request 执行请求
     * @return 执行结果流
     */
    Flux<AgentMessage> executeStream(ExecutionRequest request);

    /**
     * 停止执行
     *
     * @param chatId 聊天ID
     * @return 是否成功停止
     */
    boolean stopExecution(String chatId);

    /**
     * 是否支持该执行模式
     *
     * @param request 执行请求
     * @return 是否支持
     */
    boolean supports(ExecutionRequest request);
}
