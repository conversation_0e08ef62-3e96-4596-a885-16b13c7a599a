package cn.genn.ai.agent.orcha.shared.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 聊天模式枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum ChatMode {

    /**
     * mock模式 - 适用于测试和模拟场景
     */
    MOCK("mock", "模拟模式"),

    /**
     * 通用模式 - 适用于大多数任务，使用通用的Agent模式
     */
    COMMON("common", "通用模式"),

    /**
     * 深度研究模式 - 适用于需要深入分析和研究的任务
     */
    DEEP_RESEARCH("deepresearch", "深度研究模式"),;

    /**
     * 模式代码
     */
    @JsonValue
    @EnumValue
    private final String code;

    /**
     * 模式描述
     */
    private final String description;

    /**
     * 根据代码获取执行模式
     */
    public static ChatMode fromCode(String code) {
        for (ChatMode mode : values()) {
            if (mode.getCode().equals(code)) {
                return mode;
            }
        }
        throw new IllegalArgumentException("未知的执行模式: " + code);
    }
}
