package cn.genn.ai.agent.orcha.framework.workflow.sse.converter;

import cn.genn.ai.agent.orcha.api.dto.AgentMessage;
import cn.genn.ai.agent.orcha.api.dto.ExecutionRequest;
import cn.genn.ai.agent.orcha.api.dto.message.ToolMessage;
import cn.genn.ai.agent.orcha.framework.workflow.sse.CerebroEventType;
import cn.genn.ai.agent.orcha.framework.workflow.sse.converter.xml.XmlTagBuffer;
import cn.genn.ai.agent.orcha.framework.workflow.sse.converter.xml.XmlTagBufferManager;
import cn.genn.ai.agent.orcha.shared.enums.AreaType;
import cn.genn.ai.agent.orcha.shared.enums.CardType;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 工具事件转换器
 *
 * 负责聚合toolCall、toolParams、toolResponse三种事件
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
//@Component
public class ToolEventConverter extends BaseEventConverter {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * 工具调用状态缓存
     * Key: toolId, Value: ToolCallContext
     */
    private final ConcurrentMap<String, ToolCallContext> toolCallCache = new ConcurrentHashMap<>();

    @Override
    public boolean supports(String eventType) {
        return CerebroEventType.TOOL_CALL.getCode().equals(eventType) ||
            CerebroEventType.TOOL_PARAMS.getCode().equals(eventType) ||
            CerebroEventType.TOOL_RESPONSE.getCode().equals(eventType);
    }

    @Override
    public List<AgentMessage> convert(ServerSentEvent<String> event, ExecutionRequest request, String taskId) {
        try {
            String eventType = event.event();
            String data = event.data();

            if (StrUtil.isBlank(data)) {
                return null;
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> dataMap = OBJECT_MAPPER.readValue(data, Map.class);

            return switch (eventType) {
                case "toolCall" -> Collections.singletonList(handleToolCall(dataMap, taskId));
                case "toolParams" -> Collections.singletonList(handleToolParams(dataMap, taskId));
                case "toolResponse" -> Collections.singletonList(handleToolResponse(dataMap, taskId));
                default -> null;
            };

        } catch (Exception e) {
            log.error("转换工具事件失败，eventType: {}", event.event(), e);
            return null;
        }
    }

    /**
     * 处理工具调用事件
     */
    private AgentMessage handleToolCall(Map<String, Object> dataMap, String taskId) {
        @SuppressWarnings("unchecked")
        Map<String, Object> tool = (Map<String, Object>) dataMap.get("tool");
        if (tool == null) {
            return null;
        }

        String toolId = (String) tool.get("id");
        String toolName = (String) tool.get("toolName");
        String functionName = (String) tool.get("functionName");
        String toolAvatar = (String) tool.get("toolAvatar");

        if (StrUtil.isBlank(toolId)) {
            return null;
        }

        // 创建工具调用上下文
        ToolCallContext context = new ToolCallContext()
                .setToolId(toolId)
                .setToolName(toolName)
                .setFunctionName(functionName)
                .setToolAvatar(toolAvatar)
                .setTaskId(taskId);

        toolCallCache.put(toolId, context);

        // 立即返回工具调用开始消息
        ToolMessage toolMessage = new ToolMessage()
                .setTool(toolName)
                .setBrief("正在调用工具: " + toolName)
                .setStatus("calling")
                .setFinished(false);

        // 设置详细信息
        Map<String, Object> detail = new java.util.HashMap<>();
        if (functionName != null) {
            detail.put("functionName", functionName);
        }
        if (toolAvatar != null) {
            detail.put("toolAvatar", toolAvatar);
        }
        toolMessage.setDetail(detail);
        XmlTagBuffer tagBuffer = XmlTagBufferManager.getBufferIfPresent(taskId);
        XmlTagBuffer.TagState tagstate = tagBuffer.getCurrentTag();
        AreaType area = Optional.ofNullable(tagstate).map(XmlTagBuffer.TagState::getArea).orElse(null);
        String tagCardId = Optional.ofNullable(tagstate).map(XmlTagBuffer.TagState::getCardId).orElse("");

        return new AgentMessage()
                .setCardType(CardType.TOOL_CALL)
                .setTaskId(taskId)
                .setCardId("tool_call_" + toolId)
                .setStatus(1) // 进行中
                .setEditable(false)
                .setStarted(System.currentTimeMillis() / 1000)
                .setParentCardId(tagCardId)
                .setArea(area)
                .setMessages(List.of(toolMessage));
    }

    /**
     * 处理工具参数事件（流式聚合）
     */
    private AgentMessage handleToolParams(Map<String, Object> dataMap, String dataId) {
        @SuppressWarnings("unchecked")
        Map<String, Object> tool = (Map<String, Object>) dataMap.get("tool");
        if (tool == null) {
            return null;
        }

        String toolId = (String) tool.get("id");
        String params = (String) tool.get("params");

        if (StrUtil.isBlank(toolId)) {
            return null;
        }

        // 聚合参数
        ToolCallContext context = toolCallCache.get(toolId);
        if (context != null) {
            context.appendParams(params);
        }

        // 工具参数事件不立即输出，等待工具响应时一起输出
        return null;
    }

    /**
     * 处理工具响应事件
     */
    private AgentMessage handleToolResponse(Map<String, Object> dataMap, String taskId) {
        @SuppressWarnings("unchecked")
        Map<String, Object> tool = (Map<String, Object>) dataMap.get("tool");
        if (tool == null) {
            return null;
        }

        String toolId = (String) tool.get("id");
        String toolName = (String) tool.get("toolName");
        String params = (String) tool.get("params");
        String response = (String) tool.get("response");

        if (StrUtil.isBlank(toolId)) {
            return null;
        }

        // 获取并清理上下文
        ToolCallContext context = toolCallCache.remove(toolId);
        if (context == null) {
            // 如果没有上下文，创建一个简单的响应
            context = new ToolCallContext()
                    .setToolId(toolId)
                    .setToolName(toolName)
                    .setTaskId(taskId);
        }

        // 合并参数
        String finalParams = StrUtil.isNotBlank(context.getParams()) ? context.getParams() : params;

        ToolMessage toolMessage = new ToolMessage()
                .setTool(toolName != null ? toolName : context.getToolName())
                .setBrief("工具调用完成: " + (toolName != null ? toolName : context.getToolName()))
                .setResult("工具执行成功")
                .setStatus("completed")
                .setFinished(true);

        // 设置详细信息
        Map<String, Object> detail = new java.util.HashMap<>();
        if (finalParams != null) {
            detail.put("params", finalParams);
        }
        if (response != null) {
            detail.put("response", response);
        }
        toolMessage.setDetail(detail);

        XmlTagBuffer tagBuffer = XmlTagBufferManager.getBufferIfPresent(taskId);
        XmlTagBuffer.TagState tagstate = tagBuffer.getCurrentTag();
        AreaType area = Optional.ofNullable(tagstate).map(XmlTagBuffer.TagState::getArea).orElse(null);
        String tagCardId = Optional.ofNullable(tagstate).map(XmlTagBuffer.TagState::getCardId).orElse("");

        return new AgentMessage()
                .setCardType(CardType.TOOL_CALL)
                .setTaskId(taskId)
                .setCardId("tool_response_" + toolId)
                .setStatus(0) // 完成
                .setEditable(false)
                .setParentCardId(tagCardId)
                .setArea(area)
                .setStarted(System.currentTimeMillis() / 1000)
                .setMessages(List.of(toolMessage));
    }

    @Override
    public int getOrder() {
        return 20;
    }

    /**
     * 清理指定任务的工具调用缓存
     */
    public void clearTaskCache(String taskId) {
        toolCallCache.entrySet().removeIf(entry -> taskId.equals(entry.getValue().getTaskId()));
    }

    /**
     * 工具调用上下文
     */
    @Data
    @Accessors(chain = true)
    private static class ToolCallContext {
        private String toolId;
        private String toolName;
        private String functionName;
        private String toolAvatar;
        private String chatId;
        private String taskId;
        private final StringBuilder paramsBuilder = new StringBuilder();

        public void appendParams(String params) {
            if (params != null) {
                paramsBuilder.append(params);
            }
        }

        public String getParams() {
            return paramsBuilder.toString();
        }
    }
}
