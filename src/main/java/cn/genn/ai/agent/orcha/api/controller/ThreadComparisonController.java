package cn.genn.ai.agent.orcha.api.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
public class ThreadComparisonController {

    @GetMapping("/thread-comparison")
    public Mono<Map<String, Object>> compareThreads() {
        Map<String, Object> result = new HashMap<>();

        // 1. 当前Web请求线程（Spring管理）
        Thread webThread = Thread.currentThread();
        result.put("webThread", Map.of(
            "name", webThread.getName(),
            "isVirtual", webThread.isVirtual(),
            "class", webThread.getClass().getSimpleName()
        ));

        // 2. Reactor boundedElastic线程
        return Mono.fromCallable(() -> {
                Thread reactorThread = Thread.currentThread();
                result.put("reactorThread", Map.of(
                    "name", reactorThread.getName(),
                    "isVirtual", reactorThread.isVirtual(),
                    "class", reactorThread.getClass().getSimpleName()
                ));
                return result;
            })
            .subscribeOn(Schedulers.boundedElastic());
    }
}
