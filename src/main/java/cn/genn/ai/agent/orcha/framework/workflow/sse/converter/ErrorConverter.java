package cn.genn.ai.agent.orcha.framework.workflow.sse.converter;

import cn.genn.ai.agent.orcha.api.dto.AgentMessage;
import cn.genn.ai.agent.orcha.api.dto.ExecutionRequest;
import cn.genn.ai.agent.orcha.api.dto.message.TextMessage;
import cn.genn.ai.agent.orcha.framework.workflow.sse.CerebroEventType;
import cn.genn.ai.agent.orcha.shared.enums.CardType;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 异常实践转换器
 */
@Slf4j
@Component
public class ErrorConverter extends BaseEventConverter {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    @Override
    public boolean supports(String eventType) {
        return CerebroEventType.WORKFLOW_FAILED.getCode().equals(eventType)
            || CerebroEventType.ERROR.getCode().equals(eventType);
    }

    @Override
    public List<AgentMessage> convert(ServerSentEvent<String> event, ExecutionRequest request, String taskId) {
        try {
            String eventType = event.event();
            String data = event.data();

            if (StrUtil.isBlank(data)) {
                return null;
            }
            log.warn("异常事件，eventType: {}, data: {}", eventType, data);
            @SuppressWarnings("unchecked")
            Map<String, Object> dataMap = OBJECT_MAPPER.readValue(data, Map.class);
            String errorMessage = (String) dataMap.get("error");
            AgentMessage agentMessage = createErrorMessage(errorMessage, taskId);
            return Collections.singletonList(handleArea(agentMessage, request, taskId));

        } catch (Exception e) {
            log.error("转换工具事件失败，eventType: {}", event.event(), e);
            return null;
        }
    }
    protected AgentMessage createErrorMessage(String message,String taskId) {
        return new AgentMessage()
            .setCardType(CardType.ERROR)
            .setMessages(List.of(new TextMessage().setText(message)))
            .setTaskId(taskId)
            .setStatus(0);
    }

    @Override
    public int getOrder() {
        return 40;
    }
}
