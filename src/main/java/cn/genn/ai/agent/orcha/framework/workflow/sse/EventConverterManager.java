package cn.genn.ai.agent.orcha.framework.workflow.sse;

import cn.genn.ai.agent.orcha.api.dto.AgentMessage;
import cn.genn.ai.agent.orcha.api.dto.ExecutionRequest;
import cn.genn.ai.agent.orcha.biz.chat.AgentMessageManager;
import cn.genn.ai.agent.orcha.framework.workflow.sse.converter.EventConverter;
import cn.genn.ai.agent.orcha.framework.workflow.sse.converter.xml.XmlTagParser;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 事件转换器管理器
 *
 * 负责管理所有事件转换器，提供统一的转换入口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class EventConverterManager {

    /**
     * 所有事件转换器（按优先级排序）
     */
    private final List<EventConverter> converters;

    /**
     * 消息偏移量计数器（每个任务独立）- 降级使用
     */
    private final ConcurrentMap<String, AtomicInteger> offsetCounters =
            new ConcurrentHashMap<>();

    @Autowired
    public EventConverterManager(List<EventConverter> converters,
                               AgentMessageManager messageManager) {
        // 按优先级排序
        this.converters = converters.stream()
                .sorted((a, b) -> Integer.compare(a.getOrder(), b.getOrder()))
                .toList();

        log.info("初始化事件转换器管理器，共加载 {} 个转换器", converters.size());
        converters.forEach(converter ->
            log.debug("加载转换器: {} (优先级: {})", converter.getClass().getSimpleName(), converter.getOrder())
        );
    }

    /**
     * 转换SSE事件为AgentMessage
     *
     * @param event SSE事件
     * @param taskId 任务ID
     * @return AgentMessage，如果没有合适的转换器则返回null
     */
    public List<AgentMessage> convert(ServerSentEvent<String> event, ExecutionRequest request, String taskId) {
        String eventType = event.event();
        if (eventType == null) {
            log.debug("事件类型为空，跳过转换");
            return null;
        }

        // 查找支持该事件类型的转换器
        for (EventConverter converter : converters) {
            if (converter.supports(eventType)) {
                try {
                    return converter.convert(event, request, taskId);
                } catch (Exception e) {
                    log.error("转换器 {} 处理事件类型 {} 时发生异常",
                            converter.getClass().getSimpleName(), eventType, e);
                }
            }
        }

        log.debug("未找到支持事件类型 {} 的转换器", eventType);
        return null;
    }

    /**
     * 获取支持的事件类型列表
     */
    public List<String> getSupportedEventTypes() {
        return converters.stream()
                .flatMap(converter -> {
                    // 这里简化处理，实际可以通过反射或注解获取支持的事件类型
                    return java.util.stream.Stream.of(converter.getClass().getSimpleName());
                })
                .toList();
    }

    /**
     * 获取转换器数量
     */
    public int getConverterCount() {
        return converters.size();
    }
}
