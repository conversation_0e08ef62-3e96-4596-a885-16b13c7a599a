package cn.genn.ai.agent.orcha.framework.workflow.sse.converter;

import cn.genn.ai.agent.orcha.api.dto.AgentMessage;
import cn.genn.ai.agent.orcha.api.dto.ExecutionRequest;
import cn.genn.ai.agent.orcha.api.dto.message.TextMessage;
import cn.genn.ai.agent.orcha.framework.workflow.sse.CerebroEventType;
import cn.genn.ai.agent.orcha.shared.enums.CardType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;


/**
 * 心跳节点转换器
 */
@Slf4j
@Component
public class HeartBeatConverter extends BaseEventConverter  {
    @Override
    public boolean supports(String eventType) {
        return CerebroEventType.HEARTBEAT.getCode().equals(eventType);
    }

    @Override
    public List<AgentMessage> convert(ServerSentEvent<String> event, ExecutionRequest request, String taskId) {
        AgentMessage agentMessage = new AgentMessage()
            .setCardType(CardType.PING)
            .setMessages(List.of(new TextMessage().setText("ping")))
            .setStarted(System.currentTimeMillis() / 1000)
            .setTaskId(taskId)
            .setStatus(0);
        return Collections.singletonList(handleArea(agentMessage, request, taskId));
    }
}
