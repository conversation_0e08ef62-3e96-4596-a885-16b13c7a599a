package cn.genn.ai.agent.orcha.api.dto.message;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.function.Function;

/**
 * 思考消息
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "思考消息")
public class TextMessage {

    @Schema(description = "思考内容", example = "正在思考下一步动作")
    private String text;

}
