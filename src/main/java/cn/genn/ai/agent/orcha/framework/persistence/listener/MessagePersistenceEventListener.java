package cn.genn.ai.agent.orcha.framework.persistence.listener;

import cn.genn.ai.agent.orcha.framework.persistence.event.MessagePersistenceEvent;
import cn.genn.ai.agent.orcha.framework.persistence.service.MessagePersistenceService;
import cn.genn.spring.boot.starter.event.spring.component.SpringEventAsyncListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 消息入库事件监听器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MessagePersistenceEventListener extends SpringEventAsyncListener<MessagePersistenceEvent> {

    private final MessagePersistenceService messagePersistenceService;

    /**
     * 监听消息入库事件
     *
     * @param event 消息入库事件
     */
    @Override
    protected void onMessage(MessagePersistenceEvent event) {
        try {
            log.info("收到消息入库事件，taskId: {}, chatId: {}, taskStatus: {}",
                event.getTaskId(), event.getChatId(), event.getTaskStatus());
            messagePersistenceService.processMessagePersistence(event);
            log.info("消息入库事件处理完成，taskId: {}", event.getTaskId());
        } catch (Exception e) {
            log.error("处理消息入库事件失败，taskId: {}", event.getTaskId(), e);
        }
    }
}
