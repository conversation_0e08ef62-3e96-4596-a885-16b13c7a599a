package cn.genn.ai.agent.orcha.framework.persistence.event;

import cn.genn.ai.agent.orcha.api.dto.ExecutionRequest;
import cn.genn.ai.agent.orcha.api.dto.FileInfo;
import cn.genn.ai.agent.orcha.shared.enums.ChatMode;
import cn.genn.ai.agent.orcha.shared.enums.TaskStatus;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.spring.boot.starter.event.spring.model.SpringBaseEvent;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 消息入库事件
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Accessors(chain = true)
@Setter
@Getter
public class MessagePersistenceEvent extends SpringBaseEvent {

    /**
     * 操作类型枚举
     */
    @Getter
    @AllArgsConstructor
    public enum OperationType {
        CREATE("创建消息记录"),
        UPDATE("更新消息记录");

        private final String description;
    }

    /**
     * 操作类型
     */
    private OperationType operationType;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 聊天ID
     */
    private String chatId;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 任务状态
     */
    private TaskStatus taskStatus;

    /**
     * 用户输入
     */
    private String userInput;

    /**
     * 格式化后的用户输入
     */
    private String formattedInput;

    /**
     * 文件信息
     */
    private List<FileInfo> fileInfo;

    /**
     * 聊天模式
     */
    private ChatMode chatMode;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 会话标题
     */
    private String sessionTitle;

    /**
     * 额外配置
     */
    private String extraConfig;

    public MessagePersistenceEvent(Object source) {
        super(source);
    }

    public static MessagePersistenceEvent fromExecutionRequest(ExecutionRequest request, TaskStatus taskStatus, Object source) {
        return fromExecutionRequest(request, taskStatus, OperationType.UPDATE, source);
    }

    public static MessagePersistenceEvent fromExecutionRequest(ExecutionRequest request, TaskStatus taskStatus, OperationType operationType, Object source) {
        return new MessagePersistenceEvent(source)
            .setOperationType(operationType)
            .setTaskId(request.getTaskId())
            .setChatId(request.getChatId())
            .setAppId(request.getAppId())
            .setTaskStatus(taskStatus)
            .setChatMode(request.getChatMode())
            .setUserInput(request.getInput())
            .setFormattedInput(request.getFormattedInput())
            .setFileInfo(request.getFileInfo())
            .setTenantId(request.getUserInfo().getTenantId())
            .setUserId(request.getUserInfo().getUserId())
            .setUserName(request.getUserInfo().getUsername())
            .setSessionTitle(extractSessionTitleFromRequest(request))
            .setExtraConfig(extractExtraConfigFromRequest(request));
    }

    private static String extractSessionTitleFromRequest(ExecutionRequest request) {
        if (StrUtil.isNotBlank(request.getFormattedInput()) && request.getFormattedInput().length() <= 30) {
            return request.getFormattedInput();
        } else if (StrUtil.isNotBlank(request.getFormattedInput())) {
            return request.getFormattedInput().substring(0, 30);
        }
        return "新对话";
    }

    /**
     * 从请求中提取额外配置
     */
    private static String extractExtraConfigFromRequest(ExecutionRequest request) {
        if (request.getParameters() != null && !request.getParameters().isEmpty()) {
            return JsonUtils.toJson(request.getParameters());
        }
        return null;
    }
}
