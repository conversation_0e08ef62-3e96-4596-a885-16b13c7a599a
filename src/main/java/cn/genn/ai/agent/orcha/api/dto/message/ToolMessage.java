package cn.genn.ai.agent.orcha.api.dto.message;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * 工具消息
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "工具消息")
public class ToolMessage {

    @Schema(description = "工具名称", example = "webSearch")
    private String tool;

    @Schema(description = "简短概述", example = "正在搜索网页")
    private String brief;

    @Schema(description = "工具详细信息")
    private Map<String, Object> detail;

    @Schema(description = "结果总结", example = "找到多个相关结果")
    private String result;

    @Schema(description = "原始信息")
    private Map<String, Object> information;

    @Schema(description = "工具状态", example = "running")
    private String status = "running";

    @Schema(description = "是否完成", example = "false")
    private Boolean finished = false;
}
