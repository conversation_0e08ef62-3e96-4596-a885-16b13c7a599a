package cn.genn.ai.agent.orcha.api.controller;

import cn.genn.ai.agent.orcha.api.dto.AgentMessage;
import cn.genn.ai.agent.orcha.framework.workflow.sse.converter.xml.XmlTagParser;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/chat")
public class TestController {

    @Resource
    private XmlTagParser xmlTagParser;

    @GetMapping("/test")
    public String test() {
        List<String> list = new ArrayList<>();

//// 测试场景1: 标签在中间被拆分
//        list.add("<think");
//        list.add("ing>这是思考内容的第一部分");
//
//        // 测试场景2: 内容在中间被拆分
//        list.add("这是思考内容的第二部分，包含<step>第一");
//        list.add("步骤</step>的说明");
//
//        // 测试场景3: 结束标签被拆分
//        list.add("这是思考内容的最后部分</think");
//        list.add("ing>");
//
//        // 测试场景4: 完整的嵌套标签结构被拆分在多个部分
//        list.add("<thinking>以下是思考过程：\\n<step>第一步</st");
//        list.add("ep>：\\n<summary>打开冰");
//        list.add("箱</summary>！\\n<step>第二步</step>：");
//        list.add("\\n<summary>大象转进去</summary>！！\\n</thinking>");
//
//        // 测试场景5: 多个标签之间的文本
//        list.add("中间的文本内容<assist");
//        list.add("ant>\t以下是正式回答：");
//
//        // 测试场景6: 标签属性和内容被拆分
//        list.add("<question>你刚才的问题是如何把大象装");
//        list.add("冰箱</question>\n\t答案是需要三步：");
//
//        // 测试场景7: 空标签和连续标签
//        list.add("<tool>1.打开</tool>\n\t<tool>2.装</tool>\n\t<tool></to");
//        list.add("ol></assistant>");
//
//        // 测试场景8: 结束文本
//        list.add("我的回答结束");
//
//        // 测试场景9: 标签内包含特殊字符
//        list.add("<thinking>这里包含特殊字符：!@#$%^&*()</th");
//        list.add("inking>");
//
//        // 测试场景10: 不完整的XML结构
//        list.add("<step>这是一个没有结束的步骤");
//        list.add("继续步骤内容...");
//
//        // 测试场景11: 完整的单行标签
//        list.add("<step>这是一个完整的步骤</step>");
//
//        // 测试场景12: 复杂的嵌套结构
//        list.add("<assistant><thinking>嵌套思考<step>嵌套步骤</st");
//        list.add("ep></thinking>继续回答</assistant>");
//
//        // 测试场景13: 纯文本
//        list.add("这是一段纯文本，没有任何标签");
//
//        // 测试场景14: 标签名称被拆分
//        list.add("<sum");
//        list.add("mary>这是一个摘要</summary>");
//
//        // 测试场景15: 多个纯文本重复
//        list.add("这是第一段纯文本");
//        list.add("这是第二段纯文本");
//        list.add("这是第三段纯文本");
//        list.add("这是第四段纯文本");
//        list.add("这是第五段纯文本");
//
//        // 测试场景16: 纯文本与标签交替出现
//        list.add("纯文本1");
//        list.add("<step>步骤1</step>");
//        list.add("纯文本2");
//        list.add("<step>步骤2</step>");
//        list.add("纯文本3");
//
//        // 测试场景17: 重复的相同内容
//        list.add("重复内容");
//        list.add("重复内容");
//        list.add("重复内容");

        // 测试场景18: 空白内容和特殊字符
        list.add("<research_end>用时5分钟</research_end>123445");
        list.add("<share_url>http://www.baidu.com</share_url>");
        list.add("### 1. **古代至近代（战国时期-1949年）**");
        list.add("###");
        list.add("### ");
        list.add(" 1. **");
        list.add("bbb");
        list.add("");
        list.add(" ");
        list.add("\n");
        list.add("\t");
        list.add("特殊字符：\r\n\t");
        for (String s : list) {
            List<AgentMessage> agentMessage = getParses(s, "taskId");
            log.info(JsonUtils.toJson(agentMessage));
        }
        return "success";
    }


    private List<AgentMessage> getParses(String content,String taskId) {

        List<AgentMessage> messages = new ArrayList<>();
        List<AgentMessage> message = xmlTagParser.parse(content, taskId, null);
        if(ObjUtil.isNotNull(message)){
//            AgentMessage agentMessage = handleArea(message, request, taskId);
            messages.addAll(message);
        } else {
            while(ObjUtil.isNotNull(message)){
//                message = xmlTagParser.parse("", taskId);
                if(ObjUtil.isNotNull(message)){
//                    AgentMessage agentMessage = handleArea(message, request, taskId);
                    messages.addAll(message);
                }
            }
        }
        if(CollUtil.isEmpty(messages)){
            return null;
        }
        return messages;
    }
}
