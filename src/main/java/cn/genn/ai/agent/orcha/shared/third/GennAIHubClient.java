package cn.genn.ai.agent.orcha.shared.third;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;

import java.util.Map;

/**
 * Genn AI Hub客户端
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GennAIHubClient extends GennBaseClient {

    private final RestClient aiHubRestClient;

    public static final String GO_VOW_AGENT_LIST = "/toolInfo/goVow/agentList";

    /**
     * 获取格物智能体列表
     *
     * @return 智能体配置映射
     */
    @SuppressWarnings("unchecked")
    public Map<String, String> getGoVowAgentConfig(String token) {
        try {
            // 先用String接收响应
            String response = aiHubRestClient
                    .post()
                    .uri(GO_VOW_AGENT_LIST)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("token", token)
                    .retrieve()
                    .body(String.class);
            return unWarp(response, Map.class, true);

        } catch (Exception e) {
            log.error("获取格物智能体列表失败", e);
            throw new RuntimeException("获取格物智能体列表失败", e);
        }
    }
}
