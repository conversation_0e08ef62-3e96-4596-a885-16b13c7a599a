package cn.genn.ai.agent.orcha.framework.persistence.converter;

import cn.genn.ai.agent.orcha.api.dto.AgentMessage;
import cn.genn.ai.agent.orcha.api.dto.ChatHistoryDTO;
import cn.genn.ai.agent.orcha.framework.persistence.entity.AgentChatMessage;
import cn.genn.ai.agent.orcha.framework.persistence.event.MessagePersistenceEvent;
import cn.genn.ai.agent.orcha.shared.enums.AreaType;
import cn.genn.ai.agent.orcha.shared.enums.CardType;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.text.CharSequenceUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.data.redis.connection.stream.MapRecord;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface AgentMessageConverter {

    @Mapping(target = "messageId", expression = "java(record.getId().getValue())")
    @Mapping(target = "cardType", expression = "java(stringToCardType(getStringValue(record, \"cardType\")))")
    @Mapping(target = "area", expression = "java(stringToAreaType(getStringValue(record, \"area\")))")
    @Mapping(target = "chatId", expression = "java(getStringValue(record, \"chatId\"))")
    @Mapping(target = "parentCardId", expression = "java(getStringValueNotNull(record, \"parentCardId\"))")
    @Mapping(target = "taskId", expression = "java(getStringValue(record, \"taskId\"))")
    @Mapping(target = "cardId", expression = "java(getStringValue(record, \"cardId\"))")
    @Mapping(target = "status", expression = "java(stringToInteger(getStringValue(record, \"status\")))")
    @Mapping(target = "progress", expression = "java(stringToDouble(getStringValue(record, \"progress\")))")
    @Mapping(target = "editable", expression = "java(stringToBoolean(getStringValue(record, \"editable\")))")
    @Mapping(target = "started", expression = "java(stringToLong(getStringValue(record, \"started\")))")
    @Mapping(target = "messages", expression = "java(stringToList(getStringValue(record, \"messages\")))")
    AgentMessage mapRecordToAgentMessage(MapRecord<String, Object, Object> record);

    List<AgentMessage> mapRecordsToAgentMessages(List<MapRecord<String, Object, Object>> records);

    @Mapping(target = "createUserId", source = "event.userId")
    @Mapping(target = "createUserName", source = "event.userName")
    @Mapping(target = "updateUserId", source = "event.userId")
    @Mapping(target = "updateUserName", source = "event.userName")
    @Mapping(target = "userInput", source = "event.formattedInput")
    @Mapping(target = "tenantId", source = "event.tenantId")
    AgentChatMessage eventToChatMessage(MessagePersistenceEvent event);

    @Mapping(target = "question", source = "chatMessage.userInput")
    @Mapping(target = "answer", source = "chatMessage.messageContent")
    @Mapping(target = "questionTime", source = "chatMessage.createTime")
    @Mapping(target = "answerTime", source = "chatMessage.updateTime")
    ChatHistoryDTO chatMessageToHistory(AgentChatMessage chatMessage);

    List<ChatHistoryDTO> chatMessagesToHistory(List<AgentChatMessage> chatMessages);

    // 统一的字符串值提取方法
    default String getStringValue(MapRecord<String, Object, Object> record, String key) {
        Object value = record.getValue().get(key);
        return value != null ? value.toString() : null;
    }

    default String getStringValueNotNull(MapRecord<String, Object, Object> record, String key) {
        Object value = record.getValue().get(key);
        return value != null ? value.toString() : "";
    }

    default CardType stringToCardType(String value) {
        return value != null ? CardType.fromCode(value) : null;
    }

    default AreaType stringToAreaType(String value) {
        return value != null ? AreaType.fromCode(value) : null;
    }

    default Integer stringToInteger(String value) {
        return value != null ? Integer.parseInt(value) : null;
    }

    default Double stringToDouble(String value) {
        return value != null ? Double.parseDouble(value) : null;
    }

    default Boolean stringToBoolean(String value) {
        return value != null ? Boolean.parseBoolean(value) : null;
    }

    default Long stringToLong(String value) {
        return value != null ? Long.parseLong(value) : null;
    }

    @SuppressWarnings("unchecked")
    default List<Object> stringToList(String value) {
        return value != null ? JsonUtils.parse(value, List.class) : null;
    }

    // 反向映射：AgentMessage 到 Map
    default Map<String, String> agentMessageToMap(AgentMessage message) {
        if (message == null) {
            return new HashMap<>();
        }

        Map<String, String> map = new HashMap<>();

        putIfNotNull(map, "cardType", message.getCardType(), CardType::getCode);
        putIfNotNull(map, "area", message.getArea(), AreaType::getCode);
        putIfNotBlank(map, "chatId", message.getChatId());
        putIfNotBlank(map, "taskId", message.getTaskId());
        putIfNotBlank(map, "cardId", message.getCardId());
        putIfNotBlank(map, "parentCardId", message.getParentCardId());
        putIfNotNull(map, "progress", message.getProgress(), Object::toString);
        putIfNotNull(map, "status", message.getStatus(), Object::toString);
        putIfNotNull(map, "editable", message.getEditable(), Object::toString);
        putIfNotNull(map, "started", message.getStarted(), Object::toString);
        putIfNotNull(map, "messages", message.getMessages(), JsonUtils::toJson);

        return map;
    }

    // 工具方法：处理非空值
    default <T> void putIfNotNull(Map<String, String> map, String key, T value, Function<T, String> converter) {
        if (value != null) {
            map.put(key, converter.apply(value));
        }
    }

    // 工具方法：处理非空字符串
    default void putIfNotBlank(Map<String, String> map, String key, String value) {
        if (CharSequenceUtil.isNotBlank(value)) {
            map.put(key, value);
        }
    }
}
