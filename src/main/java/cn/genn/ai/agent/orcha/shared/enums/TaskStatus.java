package cn.genn.ai.agent.orcha.shared.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务状态枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum TaskStatus {

    UNKNOWN("00", "未知状态"),
    RUNNING("10", "运行中"),
    COMPLETED("20", "已完成"),
    FAILED("30", "已失败"),
    CANCELLED("40", "已取消")
    ;

    /**
     * 模式代码
     */
    @JsonValue
    @EnumValue
    private final String code;

    /**
     * 模式描述
     */
    private final String description;

    /**
     * 根据代码获取执行模式
     */
    public static TaskStatus fromCode(String code) {
        for (TaskStatus mode : values()) {
            if (mode.getCode().equals(code)) {
                return mode;
            }
        }
        throw new IllegalArgumentException("未知的任务状态: " + code);
    }
}
