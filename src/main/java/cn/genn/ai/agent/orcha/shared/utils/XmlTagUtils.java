package cn.genn.ai.agent.orcha.shared.utils;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * XML标签检测工具类
 * <p>
 * 用于检测文本中是否存在特定的XML标签（完整或不完整）
 *
 * <AUTHOR>
 */
@Slf4j
public class XmlTagUtils {

    // 开始标签正则表达式 - 匹配完整的开始标签 <tag>
    private static final Pattern START_TAG_PATTERN = Pattern.compile("<([a-zA-Z0-9_]+)>");

    // 结束标签正则表达式 - 匹配完整的结束标签 </tag>
    private static final Pattern END_TAG_PATTERN = Pattern.compile("</([a-zA-Z0-9_]+)>");

    // 不完整开始标签正则表达式 - 匹配可能的不完整开始标签，如 "<th"
    private static final Pattern INCOMPLETE_START_PATTERN = Pattern.compile("<([a-zA-Z0-9_]*)$");

    // 不完整结束标签正则表达式 - 匹配可能的不完整结束标签，如 "</th"
    private static final Pattern INCOMPLETE_END_PATTERN = Pattern.compile("</([a-zA-Z0-9_]*)$");

    // 预编译组合模式
    private static final Pattern COMBINED_TAG_PATTERN = Pattern.compile(
        "(<[a-zA-Z0-9_]+>)|(<\\/[a-zA-Z0-9_]+>)|(<[a-zA-Z0-9_]*$)|(<\\/[a-zA-Z0-9_]*$)"
    );

    /**
     * 解析文本，返回标签和文本内容的数组
     * 仅考虑指定标签列表中的标签，不在范围内的标签按文本处理
     * @param text    要解析的文本
     * @param tagList 支持的标签列表
     * @return 解析结果数组
     */
    public static List<XmlElement> parseTextSpecificTag(String text, String[] tagList) {
        if (text == null || text.isEmpty()) {
            return new ArrayList<>();
        }

        // 如果标签列表为空，则所有标签都按文本处理
        if (tagList == null || tagList.length == 0) {
            List<XmlElement> elements = new ArrayList<>();
            elements.add(new XmlElement(ElementType.TEXT, text, null, false, false));
            return elements;
        }

        List<XmlElement> elements = new ArrayList<>();

        // 使用预编译的组合模式
        Matcher matcher = COMBINED_TAG_PATTERN.matcher(text);

        int lastEnd = 0;
        StringBuilder textBuffer = new StringBuilder();

        // 查找所有标签
        while (matcher.find()) {
            int start = matcher.start();

            // 如果标签前有文本，添加到文本缓冲区
            if (start > lastEnd) {
                textBuffer.append(text.substring(lastEnd, start));
            }

            // 获取标签内容
            String tagContent = matcher.group();
            boolean isRecognizedTag = false;

            // 判断标签类型
            if (START_TAG_PATTERN.matcher(tagContent).matches()) {
                // 完整的开始标签
                String tagName = tagContent.substring(1, tagContent.length() - 1);
                // 只处理指定标签列表中的标签
                if (isInTagList(tagName, tagList)) {
                    // 如果文本缓冲区有内容，先添加文本元素
                    if (textBuffer.length() > 0) {
                        elements.add(new XmlElement(ElementType.TEXT, textBuffer.toString(), null, false, false));
                        textBuffer.setLength(0);
                    }
                    elements.add(new XmlElement(ElementType.TAG, tagContent, tagName, true, true));
                    isRecognizedTag = true;
                }
            } else if (END_TAG_PATTERN.matcher(tagContent).matches()) {
                // 完整的结束标签
                String tagName = tagContent.substring(2, tagContent.length() - 1);
                // 只处理指定标签列表中的标签
                if (isInTagList(tagName, tagList)) {
                    // 如果文本缓冲区有内容，先添加文本元素
                    if (textBuffer.length() > 0) {
                        elements.add(new XmlElement(ElementType.TEXT, textBuffer.toString(), null, false, false));
                        textBuffer.setLength(0);
                    }
                    elements.add(new XmlElement(ElementType.TAG, tagContent, tagName, true, false));
                    isRecognizedTag = true;
                }
            } else if (INCOMPLETE_START_PATTERN.matcher(tagContent).matches()) {
                // 不完整的开始标签
                String tagName = tagContent.substring(1);
                // 检查是否是支持的标签的前缀
                for (String supportedTag : tagList) {
                    if (supportedTag.startsWith(tagName)) {
                        // 如果文本缓冲区有内容，先添加文本元素
                        if (textBuffer.length() > 0) {
                            elements.add(new XmlElement(ElementType.TEXT, textBuffer.toString(), null, false, false));
                            textBuffer.setLength(0);
                        }
                        elements.add(new XmlElement(ElementType.TAG, tagContent, tagName, false, true));
                        isRecognizedTag = true;
                        break;
                    }
                }
            } else if (INCOMPLETE_END_PATTERN.matcher(tagContent).matches()) {
                // 不完整的结束标签
                String tagName = tagContent.substring(2);
                // 检查是否是支持的标签的前缀
                for (String supportedTag : tagList) {
                    if (supportedTag.startsWith(tagName)) {
                        // 如果文本缓冲区有内容，先添加文本元素
                        if (textBuffer.length() > 0) {
                            elements.add(new XmlElement(ElementType.TEXT, textBuffer.toString(), null, false, false));
                            textBuffer.setLength(0);
                        }
                        elements.add(new XmlElement(ElementType.TAG, tagContent, tagName, false, false));
                        isRecognizedTag = true;
                        break;
                    }
                }
            }

            // 如果不是识别的标签，则当作普通文本处理
            if (!isRecognizedTag) {
                textBuffer.append(tagContent);
            }

            lastEnd = matcher.end();
        }

        // 处理剩余的文本
        if (lastEnd < text.length()) {
            textBuffer.append(text.substring(lastEnd));
        }

        // 如果文本缓冲区有内容，添加最后的文本元素
        if (textBuffer.length() > 0) {
            elements.add(new XmlElement(ElementType.TEXT, textBuffer.toString(), null, false, false));
        }

        return elements;
    }

    /**
     * 检查文本中是否存在指定标签列表中的标签（完整或不完整）,即是否纯文本
     */
    public static boolean containsSpecificTags(List<XmlElement> elements) {
        return elements.size() != 1 || !elements.getFirst().isText();
    }

    /**
     * 文本中是否存在成对完整标签
     */
    public static boolean hasCompletePairTags(List<XmlElement> elements) {
        if (elements == null || elements.isEmpty()) {
            return false;
        }

        // 使用栈来匹配标签对
        Map<String, Integer> tagPairs = new HashMap<>();

        // 遍历所有元素
        for (XmlElement element : elements) {
            // 只处理标签元素
            if (element.isTag()) {
                String tagName = element.getTagName();

                // 如果是开始标签
                if (element.isLeftTag()) {
                    // 增加该标签名的计数
                    tagPairs.put(tagName, tagPairs.getOrDefault(tagName, 0) + 1);
                }
                // 如果是结束标签
                else {
                    // 减少该标签名的计数
                    int count = tagPairs.getOrDefault(tagName, 0);
                    if (count > 0) {
                        // 找到一对完整标签
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * 截断指定内容,保留剩余
     *
     * @param text
     * @param elements
     * @return
     */
    public static String truncateElements(String text, List<XmlElement> elements) {
        if (text == null || text.isEmpty() || elements == null || elements.isEmpty()) {
            return text;
        }

        String remainingText = text;

        for (XmlElement element : elements) {
            if (element.isText()) {
                // 如果是文本元素，直接截断内容
                int index = remainingText.indexOf(element.getContent());
                if (index >= 0) {
                    remainingText = remainingText.substring(index + element.getContent().length());
                }
            } else if (element.isTag()) {
                // 如果是标签元素，根据标签类型构造完整标签
                String tagContent = element.tagNameAll();
                int index = remainingText.indexOf(tagContent);
                if (index >= 0) {
                    remainingText = remainingText.substring(index + tagContent.length());
                }
            }
        }

        return remainingText;
    }


    /**
     * 检查标签名是否在指定的标签列表中
     *
     * @param tagName 标签名
     * @param tagList 标签列表
     * @return 是否在列表中
     */
    private static boolean isInTagList(String tagName, String[] tagList) {
        for (String tag : tagList) {
            if (tag.equals(tagName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * XML元素类型枚举
     */
    public enum ElementType {
        /**
         * 标签
         */
        TAG,

        /**
         * 文本
         */
        TEXT
    }

    /**
     * XML元素
     */
    @Data
    @AllArgsConstructor
    public static class XmlElement {
        /**
         * 元素类型：标签或文本
         */
        private ElementType type;

        /**
         * 元素内容
         */
        private String content;

        /**
         * 标签名（如果是标签）
         */
        private String tagName;

        /**
         * 标签是否完整
         */
        private boolean complete;

        /**
         * 是否为左标签（开始标签）
         */
        private boolean leftTag;

        /**
         * 检查是否为文本元素
         */
        public boolean isText() {
            return type == ElementType.TEXT;
        }

        /**
         * 检查是否为标签元素
         */
        public boolean isTag() {
            return type == ElementType.TAG;
        }

        public String tagNameAll() {
            return leftTag ? "<" + tagName + ">" : "</" + tagName + ">";
        }
    }
}
