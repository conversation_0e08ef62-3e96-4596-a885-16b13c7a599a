package cn.genn.ai.agent.orcha.framework.workflow.sse.converter.xml;

import cn.genn.ai.agent.orcha.api.dto.message.TextMessage;
import cn.genn.ai.agent.orcha.api.dto.message.UrlMessage;
import cn.genn.ai.agent.orcha.shared.enums.AreaType;
import cn.genn.ai.agent.orcha.shared.enums.CardType;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

/**
 * XML标签类型枚举
 * <p>
 * 使用函数式编程优雅地处理不同标签类型的消息创建逻辑
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum XmlTagType {

    /**
     * 思考标签
     */
    THINKING("thinking", CardType.THINKING, AreaType.THINK, false,
        content -> List.of(new TextMessage().setText(content))),

    /**
     * 步骤标签
     */
    STEP("step", CardType.STEP, AreaType.THINK, false,
        content -> List.of(new TextMessage().setText(content))),

    /**
     * 总结标签
     */
    SUMMARY("summary", CardType.SUMMARY, AreaType.THINK, false,
        content -> List.of(new TextMessage().setText(content))),

    /**
     * 搜索
     */
    SEARCH("search", CardType.SEARCH, AreaType.THINK, false,
        content -> List.of(new TextMessage().setText(content))),

    /**
     * 网页读取
     */
    FETCH("fetch", CardType.READ_URL, AreaType.THINK, true,
            UrlMessage::handleFetch),

    /**
     * 问题标签
     */
    QUESTION("question", CardType.QUESTION, AreaType.CONTENT, false,
        content -> List.of(new TextMessage().setText(content))),

    /**
     * 进度条标签,标识工作空间
     */
    PROGRESS("progress", CardType.PROGRESS, AreaType.WORKSPACE, false,
        content -> List.of(new TextMessage().setText(content))),

    /**
     * 报告标签
     */
    REPORT("report", CardType.REPORT, AreaType.CONTENT, false,
        content -> List.of(new TextMessage().setText(content))),

    /**
     * 分享url
     */
    SHARE_URL("share_url", CardType.SHARE_URL, AreaType.CONTENT, true,
        content -> List.of(new TextMessage().setText(content))),

    /**
     * 搜索结果
     */
    RESEARCH_END("research_end", CardType.RESEARCH_END, AreaType.CONTENT, false,
        content -> List.of(new TextMessage().setText(content))),

    /**
     * 默认普通文本
     */
    DEFAULT("", CardType.ASSISTANT, AreaType.CONTENT, false,
        content -> List.of(new TextMessage().setText(content)));

    /**
     * 标签名称
     */
    private final String tagName;

    /**
     * 卡片类型
     */
    private final CardType cardType;

    /**
     * 默认区域类型
     */
    private final AreaType areaType;

    /**
     * 等待正文完整
     */
    private final boolean waitForComplete;

    /**
     * 消息创建函数
     */
    private final Function<String, List<Object>> messageCreator;

    /**
     * 根据标签名获取对应的标签类型
     *
     * @param tagName 标签名
     * @return 标签类型，如果未找到则返回DEFAULT
     */
    public static XmlTagType fromTagName(String tagName) {
        return Arrays.stream(values())
            .filter(type -> type.getTagName().equals(tagName))
            .findFirst()
            .orElse(DEFAULT);
    }

    /**
     * 获取所有支持的标签名称（排除DEFAULT）
     *
     * @return 支持的标签名称数组
     */
    public static String[] getSupportedTagNames() {
        return Arrays.stream(values())
            .filter(type -> type != DEFAULT)
            .map(XmlTagType::getTagName)
            .toArray(String[]::new);
    }

    /**
     * 检查是否为支持的标签类型
     *
     * @param tagName 标签名
     * @return 是否支持
     */
    public static boolean isSupported(String tagName) {
        return Arrays.stream(values())
            .anyMatch(type -> type.getTagName().equals(tagName) && type != DEFAULT);
    }
}
