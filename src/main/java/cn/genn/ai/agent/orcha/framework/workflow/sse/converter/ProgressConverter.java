package cn.genn.ai.agent.orcha.framework.workflow.sse.converter;

import cn.genn.ai.agent.orcha.api.dto.AgentMessage;
import cn.genn.ai.agent.orcha.api.dto.ExecutionRequest;
import cn.genn.ai.agent.orcha.framework.workflow.sse.CerebroEventType;
import cn.genn.ai.agent.orcha.framework.workflow.sse.converter.xml.XmlTagBuffer;
import cn.genn.ai.agent.orcha.framework.workflow.sse.converter.xml.XmlTagBufferManager;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class ProgressConverter extends BaseEventConverter {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    @Override
    public boolean supports(String eventType) {
        return CerebroEventType.WORKFLOW_PROGRESS.getCode().equals(eventType);
    }

    @Override
    public List<AgentMessage> convert(ServerSentEvent<String> event, ExecutionRequest request, String taskId) {
        try {
            String data = event.data();

            Double progress = 0.0;
            if (StrUtil.isNotBlank(data)) {
                @SuppressWarnings("unchecked")
                Map<String, Object> dataMap = OBJECT_MAPPER.readValue(data, Map.class);
                progress = safeConvertToDouble(dataMap.get("progress"));

            }
            XmlTagBuffer tagBuffer = XmlTagBufferManager.getXmlTagBuffer(taskId);
            tagBuffer.setProgress(progress);
            return null;
        } catch (Exception e) {
            log.error("转换交互式事件失败: {}", event.data(), e);
            return null;
        }
    }

    private Double safeConvertToDouble(Object value){
        if(value == null){
            return 0.0;
        }
        if(value instanceof Number){
            return ((Number) value).doubleValue();
        }
        return 0.0;
    }

}
