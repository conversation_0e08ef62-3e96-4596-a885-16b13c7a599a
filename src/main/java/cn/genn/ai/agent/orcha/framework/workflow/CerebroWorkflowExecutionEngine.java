package cn.genn.ai.agent.orcha.framework.workflow;

import cn.genn.ai.agent.orcha.api.dto.AgentMessage;
import cn.genn.ai.agent.orcha.api.dto.ExecutionRequest;
import cn.genn.ai.agent.orcha.framework.execution.AbstractExecutionEngine;
import cn.genn.ai.agent.orcha.framework.workflow.dto.WorkflowRequest;
import cn.genn.ai.agent.orcha.framework.workflow.dto.WorkflowResponse;
import cn.genn.ai.agent.orcha.framework.workflow.sse.ModularSseEventProcessor;
import cn.genn.ai.agent.orcha.shared.enums.ExecutionMode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * Cerebro 工作流执行引擎
 *
 * 负责与Cerebro工作流系统的集成，处理响应转换和流式输出
 * 继承泛型父类，并指定原始事件类型为 WorkflowResponse
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CerebroWorkflowExecutionEngine extends AbstractExecutionEngine<WorkflowResponse> {

    private final WorkflowClient workflowClient;
    private final ModularSseEventProcessor sseEventProcessor;

    @Override
    protected ExecutionMode getSupportedMode() {
        return ExecutionMode.WORKFLOW;
    }

    @Override
    protected Flux<WorkflowResponse> initiateRawStream(ExecutionRequest request) {
        log.info("Cerebro引擎: 正在调用下游工作流...");
        WorkflowRequest workflowRequest = WorkflowRequest.create(request);
        return workflowClient.executeStreamRaw(workflowRequest);
    }

    @Override
    protected String extractTaskIdFromFirstEvent(WorkflowResponse firstEvent) {
        if (firstEvent != null && firstEvent.getTaskId() != null) {
            log.info("Cerebro引擎: 成功解析到 TaskId: {}", firstEvent.getTaskId());
            return firstEvent.getTaskId();
        }
        log.error("Cerebro引擎: 无法从第一个响应中解析TaskId。");
        return null;
    }

    @Override
    protected Flux<List<AgentMessage>> processStream(Flux<WorkflowResponse> rawStream, ExecutionRequest request, String chatId, String taskId) {
        log.info("Cerebro引擎: 正在处理原始SSE流。TaskId: {}", taskId);
        // 将 WorkflowResponse 流转换为 SSE 事件流
        Flux<ServerSentEvent<String>> sseFlux = rawStream
            .mapNotNull(WorkflowResponse::getContent)
            .filter(java.util.Objects::nonNull); // 过滤掉第一个不含SSE内容的响应

        // 使用已有的SseEventProcessor进行处理
        return sseEventProcessor.processEventStream(sseFlux, request, chatId, taskId);
    }

    @Override
    protected boolean doStopExecution(String appId, String taskId) {
        return workflowClient.stopExecution(appId, taskId);
    }

}
