package cn.genn.ai.agent.orcha.framework.cache;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.CompletableFuture;

import static cn.genn.ai.agent.orcha.shared.constants.CacheConstants.AGENT_TASK_CANCELLATION_KEY_PREFIX;

/**
 * 取消信号管理器
 * 管理任务的取消信号，支持分布式环境下的取消通知
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CancellationSignalManager {

    private final RedisTemplate<String, Object> redisTemplate;

    // 本地缓存，避免频繁查询Redis
    private final ConcurrentMap<String, Boolean> localCancellationCache = new ConcurrentHashMap<>();

    private static final Duration CANCELLATION_TTL = Duration.ofMinutes(30); // 30分钟后自动清理

    /**
     * 设置取消信号
     */
    public void setCancellationSignal(String taskId) {
        String key = AGENT_TASK_CANCELLATION_KEY_PREFIX + taskId;
        try {
            redisTemplate.opsForValue().set(key, true, CANCELLATION_TTL);
            localCancellationCache.put(taskId, true);
            log.info("设置取消信号成功，TaskId: {}", taskId);
        } catch (Exception e) {
            log.error("设置取消信号失败，TaskId: {}", taskId, e);
        }
    }

    /**
     * 检查是否被取消
     */
    public boolean isCancelled(String taskId) {
        // 先检查本地缓存
        Boolean localResult = localCancellationCache.get(taskId);
        if (Boolean.TRUE.equals(localResult)) {
            return true;
        }

        // 检查Redis
        String key = AGENT_TASK_CANCELLATION_KEY_PREFIX + taskId;
        try {
            Boolean result = (Boolean) redisTemplate.opsForValue().get(key);
            boolean cancelled = Boolean.TRUE.equals(result);
            if (cancelled) {
                localCancellationCache.put(taskId, true);
            }
            return cancelled;
        } catch (Exception e) {
            log.error("检查取消信号失败，TaskId: {}", taskId, e);
            return false;
        }
    }

    /**
     * 清除取消信号
     */
    public void clearCancellationSignal(String taskId) {
        String key = AGENT_TASK_CANCELLATION_KEY_PREFIX + taskId;
        try {
            // 保存当前线程的中断状态
            boolean interrupted = Thread.interrupted();

            try {
                // 在清除中断状态后执行Redis操作
                redisTemplate.delete(key);
                localCancellationCache.remove(taskId);
                log.info("清除取消信号成功，TaskId: {}", taskId);
            } finally {
                // 如果之前线程已被中断，恢复中断状态
                if (interrupted) {
                    Thread.currentThread().interrupt();
                }
            }
        } catch (Exception e) {
            log.error("清除取消信号失败，TaskId: {}", taskId, e);
        }
    }

    /**
     * 异步清除取消信号
     * 在可能的线程中断场景下使用
     */
    public void clearCancellationSignalAsync(String taskId) {
        String key = AGENT_TASK_CANCELLATION_KEY_PREFIX + taskId;
        CompletableFuture.runAsync(() -> {
            try {
                redisTemplate.delete(key);
                localCancellationCache.remove(taskId);
                log.info("异步清除取消信号成功，TaskId: {}", taskId);
            } catch (Exception e) {
                log.error("异步清除取消信号失败，TaskId: {}", taskId, e);
            }
        });
    }

    /**
     * 清除本地缓存
     */
    public void clearLocalCache(String taskId) {
        localCancellationCache.remove(taskId);
    }
}
