package cn.genn.ai.agent.orcha.shared.third;

import cn.genn.core.exception.BaseException;
import cn.genn.core.exception.CommonCode;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 */
@Slf4j
public class GennBaseClient {

    public <T> T unWarp(String result, Class<T> clazz, boolean throwEx) {
        String resultData = checkResult(result, throwEx);
        if (CharSequenceUtil.isEmpty(resultData)) {
            return null;
        }
        return JsonUtils.parse(resultData, clazz);
    }

    public <T> List<T> unWarpList(String result, Class<T> clazz, boolean throwEx) {
        String resultData = checkResult(result, throwEx);
        if (CharSequenceUtil.isEmpty(resultData)) {
            return Collections.emptyList();
        }
        return JsonUtils.parseToList(resultData, clazz);
    }

    public String checkResult(String result, boolean throwEx) {
        if (CharSequenceUtil.isBlank(result)) {
            log.error("调用内部服务异常: 返回值为空");
            if (throwEx) {
                throw new BaseException(CommonCode.CONNECT_ERROR);
            }
            return null;
        }
        JSON json = JSONUtil.parse(result);
        if (json == null) {
            log.error("调用内部服务异常: 解析json失败");
            if (throwEx) {
                throw new BaseException(CommonCode.CONNECT_ERROR);
            }
            return null;
        }
        Boolean success = (Boolean) json.getByPath("success");
        if (!Objects.equals(true, success)) {
            String code = json.getByPath("code").toString();
            String msg = json.getByPath("msg").toString();
            log.error("调用内部服务异常: code[{}],msg:[{}]", code, msg);
            throw new BaseException(CommonCode.CONNECT_ERROR);
        }
        return json.getByPath("data").toString();
    }
}
