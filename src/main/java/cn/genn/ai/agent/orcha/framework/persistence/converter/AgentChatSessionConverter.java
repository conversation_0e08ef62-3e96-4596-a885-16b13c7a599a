package cn.genn.ai.agent.orcha.framework.persistence.converter;

import cn.genn.ai.agent.orcha.api.dto.ChatSessionDTO;
import cn.genn.ai.agent.orcha.framework.persistence.entity.AgentChatSession;
import cn.genn.core.model.page.PageResultDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface AgentChatSessionConverter {

    ChatSessionDTO toChatSessionDTO(AgentChatSession agentChatSession);

    List<ChatSessionDTO> toChatSessionDTOList(List<AgentChatSession> agentChatSessions);

    @Mapping(target = "pageNo", source = "current")
    @Mapping(target = "pageSize", source = "size")
    @Mapping(target = "list", source = "records")
    @Mapping(target = "totalPages", source = "pages")
    PageResultDTO<ChatSessionDTO> toPageResult(IPage<AgentChatSession> poPage);

}
