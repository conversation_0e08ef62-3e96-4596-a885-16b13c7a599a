package cn.genn.ai.agent.orcha.shared.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 显示区域类型枚举
 */
@Getter
@AllArgsConstructor
public enum AreaType {

    /**
     * 思考过程
     */
    THINK("think", "思考过程"),

    /**
     * 回复正文
     */
    CONTENT("content", "回复正文"),

    /**
     * 工作空间
     */
    WORKSPACE("workspace", "工作空间"),

    ;
    /**
     * 类型代码
     */
    @EnumValue
    @JsonValue
    private final String code;

    /**
     * 类型描述
     */
    private final String description;


    public static AreaType fromCode(String code) {
        for (AreaType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的区域类型: " + code);
    }


}
