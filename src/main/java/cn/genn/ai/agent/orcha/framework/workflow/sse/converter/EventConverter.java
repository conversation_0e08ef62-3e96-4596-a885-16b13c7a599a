package cn.genn.ai.agent.orcha.framework.workflow.sse.converter;

import cn.genn.ai.agent.orcha.api.dto.AgentMessage;
import cn.genn.ai.agent.orcha.api.dto.ExecutionRequest;
import org.springframework.http.codec.ServerSentEvent;

import java.util.List;

/**
 * 事件转换器接口
 *
 * 定义将SSE事件转换为AgentMessage的规范
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface EventConverter {

    /**
     * 判断是否支持该事件类型
     *
     * @param eventType 事件类型
     * @return 是否支持
     */
    boolean supports(String eventType);

    /**
     * 转换事件为AgentMessage
     *
     * @param event SSE事件
     * @param taskId 任务ID
     * @return AgentMessage，如果不需要输出则返回null
     */
    List<AgentMessage> convert(ServerSentEvent<String> event, ExecutionRequest request, String taskId);

    /**
     * 获取转换器优先级
     * 数值越小优先级越高
     *
     * @return 优先级
     */
    default int getOrder() {
        return 100;
    }
}
