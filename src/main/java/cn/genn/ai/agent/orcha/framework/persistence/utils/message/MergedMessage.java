package cn.genn.ai.agent.orcha.framework.persistence.utils.message;

import cn.genn.ai.agent.orcha.shared.enums.AreaType;
import cn.genn.ai.agent.orcha.shared.enums.CardType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class MergedMessage {

    /**
     * 卡片类型
     */
    private CardType cardType;

    /**
     * 卡片ID
     */
    private String cardId;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 区域类型标识
     */
    private AreaType area;

    /**
     * 父卡片id
     */
    public String parentCardId;

    /**
     * 合并后的消息内容列表
     */
    private List<Object> messages;

    /**
     * 进度
     */
    private Double progress;

    /**
     * 原始消息数量
     */
    private Integer messageCount;

    /**
     * 第一条消息ID
     */
    private String firstMessageId;

    /**
     * 最后一条消息ID
     */
    private String lastMessageId;
}
