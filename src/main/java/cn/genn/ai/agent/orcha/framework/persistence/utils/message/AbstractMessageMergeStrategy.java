package cn.genn.ai.agent.orcha.framework.persistence.utils.message;

import cn.genn.ai.agent.orcha.api.dto.AgentMessage;
import cn.genn.ai.agent.orcha.shared.enums.CardType;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.lang.reflect.ParameterizedType;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public abstract class AbstractMessageMergeStrategy<T> implements MessageMergeStrategy{

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private final Class<T> targetType;

    @SuppressWarnings("unchecked")
    protected AbstractMessageMergeStrategy() {
        // 获取泛型类型
        this.targetType = (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass())
            .getActualTypeArguments()[0];
    }

    @Override
    public List<MergedMessage> mergeMessages(CardType cardType, List<AgentMessage> messages) {
        if (messages.isEmpty()) {
            return null;
        }
        return cardType.isShouldMerge() ? mergeMessagesToOne(messages) : convertToMergedMessages(messages);
    }

    /**
     * 合并消息内容
     */
    protected abstract List<T> mergeContent(List<T> messages);

    @SuppressWarnings("unchecked")
    private List<MergedMessage> mergeMessagesToOne(List<AgentMessage> messages) {
        AgentMessage first = messages.getFirst();
        AgentMessage last = messages.getLast();
        List<T> messageContent = messages.stream()
            .flatMap(message -> {
                List<Object> flatMessage = message.getMessages();
                if (flatMessage == null || flatMessage.isEmpty()) {
                    return null;
                }
                return flatMessage.stream();
            })
            .filter(Objects::nonNull)
            .map(this::convertToTargetType)
            .collect(Collectors.toList());
        MergedMessage mergedMessage =
            new MergedMessage()
                .setCardType(first.getCardType())
                .setArea(first.getArea())
                .setProgress(last.getProgress())
                .setParentCardId(first.getParentCardId())
                .setCardId(first.getCardId())
                .setMessageCount(messages.size())
                .setStatus(last.getStatus())
                .setFirstMessageId(first.getMessageId())
                .setLastMessageId(last.getMessageId())
                .setMessages((List<Object>) mergeContent(messageContent));
            ;
        return List.of(mergedMessage);
    }


    private List<MergedMessage> convertToMergedMessages(List<AgentMessage> messages) {
        return messages.stream()
            .map(message -> {
                return new MergedMessage()
                    .setCardType(message.getCardType())
                    .setCardId(message.getCardId())
                    .setStatus(message.getStatus())
                    .setMessages(message.getMessages())
                    .setProgress(message.getProgress())
                    .setArea(message.getArea())
                    .setParentCardId(message.getParentCardId())
                    .setMessageCount(1)
                    .setFirstMessageId(message.getMessageId())
                    .setLastMessageId(message.getMessageId());
            })
            .toList();
    }

    private T convertToTargetType(Object source) {
        if (targetType.isInstance(source)) {
            return targetType.cast(source);
        }

        // 使用ObjectMapper进行类型转换
        return objectMapper.convertValue(source, targetType);
    }

}
