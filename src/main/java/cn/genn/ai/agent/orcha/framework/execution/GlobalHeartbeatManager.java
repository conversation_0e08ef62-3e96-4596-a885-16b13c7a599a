package cn.genn.ai.agent.orcha.framework.execution;

import cn.genn.ai.agent.orcha.api.dto.AgentMessage;
import cn.genn.ai.agent.orcha.api.dto.message.TextMessage;
import cn.genn.ai.agent.orcha.biz.chat.AgentMessageManager;
import cn.genn.ai.agent.orcha.framework.cache.RedisStreamManager;
import cn.genn.ai.agent.orcha.framework.persistence.converter.AgentMessageConverter;
import cn.genn.ai.agent.orcha.shared.enums.CardType;
import cn.genn.ai.agent.orcha.shared.enums.TaskStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 全局心跳管理器
 * 负责向所有活跃的客户端连接推送心跳消息，保持连接活跃
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GlobalHeartbeatManager {

    private final RedisStreamManager redisStreamManager;
    private final AgentMessageManager messageManager;
    private final AgentMessageConverter agentMessageConverter;

    // 维护活跃的任务ID集合
    private final Set<String> activeTaskIds = ConcurrentHashMap.newKeySet();

    // 心跳线程池
    private final ScheduledExecutorService heartbeatScheduler = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread thread = new Thread(r, "heartbeat-sender");
        thread.setDaemon(true);
        return thread;
    });

    // 清理线程池
    private final ScheduledExecutorService cleanupScheduler = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread thread = new Thread(r, "heartbeat-cleanup");
        thread.setDaemon(true);
        return thread;
    });

    // 心跳间隔（秒）
    private static final int HEARTBEAT_INTERVAL = 10;
    // 清理间隔（秒）
    private static final int CLEANUP_INTERVAL = 60;

    /**
     * 应用启动后开始心跳任务
     */
    @EventListener(ApplicationReadyEvent.class)
    public void startHeartbeat() {
        log.info("启动全局心跳管理器");

        // 启动心跳发送任务
        heartbeatScheduler.scheduleWithFixedDelay(
            this::sendHeartbeatToAllClients,
            HEARTBEAT_INTERVAL,
            HEARTBEAT_INTERVAL,
            TimeUnit.SECONDS
        );

        // 启动清理任务
        cleanupScheduler.scheduleWithFixedDelay(
            this::cleanupInactiveTaskIds,
            CLEANUP_INTERVAL,
            CLEANUP_INTERVAL,
            TimeUnit.SECONDS
        );
    }

    /**
     * 注册活跃任务
     */
    public void registerActiveTask(String taskId) {
        activeTaskIds.add(taskId);
        log.debug("注册活跃任务: {}", taskId);
    }

    /**
     * 注销任务
     */
    public void unregisterTask(String taskId) {
        activeTaskIds.remove(taskId);
        log.debug("注销任务: {}", taskId);
    }

    /**
     * 向所有客户端发送心跳消息
     */
    private void sendHeartbeatToAllClients() {
        if (activeTaskIds.isEmpty()) {
            log.debug("没有活跃任务，跳过心跳发送");
            return;
        }

        log.debug("开始向{}个活跃任务发送心跳", activeTaskIds.size());


        // 并发发送心跳到所有活跃任务
        activeTaskIds.parallelStream().forEach(taskId -> {
            try {
                Map<String, String> heartbeatMessage = createHeartbeatMessage(taskId);
                redisStreamManager.addMessage(taskId, heartbeatMessage);
                log.debug("心跳消息已发送到任务: {}", taskId);
            } catch (Exception e) {
                log.warn("发送心跳消息失败，taskId: {}", taskId, e);
                // 发送失败时标记为待清理
                scheduleForCleanup(taskId);
            }
        });
    }

    /**
     * 清理非活跃的任务ID
     */
    private void cleanupInactiveTaskIds() {
        if (activeTaskIds.isEmpty()) {
            return;
        }

        log.debug("开始清理非活跃任务，当前活跃任务数: {}", activeTaskIds.size());

        activeTaskIds.removeIf(taskId -> {
            try {
                TaskStatus status = messageManager.getTaskStatus(taskId);
                boolean shouldRemove = (status == TaskStatus.COMPLETED ||
                    status == TaskStatus.FAILED ||
                    status == TaskStatus.CANCELLED);

                if (shouldRemove) {
                    log.debug("清理已结束的任务: {}, 状态: {}", taskId, status);
                }

                return shouldRemove;
            } catch (Exception e) {
                log.warn("检查任务状态失败，移除任务: {}", taskId, e);
                return true; // 出错时也移除
            }
        });
    }

    /**
     * 创建心跳消息
     */
    private Map<String, String> createHeartbeatMessage(String taskId) {
        AgentMessage agentMessage = new AgentMessage()
            .setCardType(CardType.PING)
            .setMessages(List.of(new TextMessage().setText("ping")))
            .setTaskId(taskId)
            .setStatus(0);
        return agentMessageConverter.agentMessageToMap(agentMessage);
    }

    /**
     * 标记任务为待清理
     */
    private void scheduleForCleanup(String taskId) {
        // 延迟移除，避免临时网络问题导致的误删
        cleanupScheduler.schedule(() -> {
            activeTaskIds.remove(taskId);
            log.debug("延迟清理任务: {}", taskId);
        }, 5, TimeUnit.MINUTES);
    }

    /**
     * 获取当前活跃任务数
     */
    public int getActiveTaskCount() {
        return activeTaskIds.size();
    }
}
