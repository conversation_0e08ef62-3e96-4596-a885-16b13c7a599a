package cn.genn.ai.agent.orcha.framework.persistence.utils.message;

import cn.genn.ai.agent.orcha.api.dto.message.TextMessage;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class TextMessageMergeStrategy extends AbstractMessageMergeStrategy<TextMessage> {

    @Override
    protected List<TextMessage> mergeContent(List<TextMessage> messages) {
        if (messages == null || messages.isEmpty()) {
            return messages;
        }

        // 将所有消息的文本内容拼接
        String mergedText = messages.stream()
            .filter(message -> message != null && message.getText() != null)
            .map(TextMessage::getText)
            .collect(Collectors.joining());

        // 创建合并后的消息
        TextMessage mergedMessage = new TextMessage();
        mergedMessage.setText(mergedText);

        return List.of(mergedMessage);
    }
}
