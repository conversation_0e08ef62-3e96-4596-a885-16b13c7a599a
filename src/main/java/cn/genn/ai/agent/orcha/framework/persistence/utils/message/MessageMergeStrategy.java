package cn.genn.ai.agent.orcha.framework.persistence.utils.message;

import cn.genn.ai.agent.orcha.api.dto.AgentMessage;
import cn.genn.ai.agent.orcha.shared.enums.CardType;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MessageMergeStrategy {

    /**
     * 合并消息
     *
     * @param messages 连续的相同类型消息
     * @return 合并后的消息对象
     */
    List<MergedMessage> mergeMessages(CardType cardType, List<AgentMessage> messages);
}
