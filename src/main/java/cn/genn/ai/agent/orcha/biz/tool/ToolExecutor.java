package cn.genn.ai.agent.orcha.biz.tool;

import cn.genn.ai.agent.orcha.api.dto.message.ToolMessage;
import reactor.core.publisher.Flux;

import java.util.Map;

/**
 * 工具执行器接口
 *
 * 定义工具执行的统一规范
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ToolExecutor {

    /**
     * 同步执行工具
     *
     * @param toolName 工具名称
     * @param parameters 工具参数
     * @return 工具消息
     */
    ToolMessage execute(String toolName, Map<String, Object> parameters);

    /**
     * 流式执行工具
     *
     * @param toolName 工具名称
     * @param parameters 工具参数
     * @return 工具消息流
     */
    Flux<ToolMessage> executeStream(String toolName, Map<String, Object> parameters);

    /**
     * 是否支持该工具
     *
     * @param toolName 工具名称
     * @return 是否支持
     */
    boolean supports(String toolName);

    /**
     * 获取工具描述
     *
     * @param toolName 工具名称
     * @return 工具描述
     */
    String getToolDescription(String toolName);

    /**
     * 获取支持的工具列表
     *
     * @return 工具名称列表
     */
    String[] getSupportedTools();
}
