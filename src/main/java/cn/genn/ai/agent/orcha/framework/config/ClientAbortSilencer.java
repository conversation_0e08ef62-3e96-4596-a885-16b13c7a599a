package cn.genn.ai.agent.orcha.framework.config;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.util.DisconnectedClientHelper;

import java.io.IOException;

/**
 * <AUTHOR>
 */
@ControllerAdvice
public class ClientAbortSilencer {

    private final DisconnectedClientHelper helper =
        new DisconnectedClientHelper(ClientAbortSilencer.class.getName());

    @ExceptionHandler(IOException.class)
    public void swallow(HttpServletRequest req, IOException ex) {
        if (helper.checkAndLogClientDisconnectedException(ex)) {
            return;
        }
    }
}
