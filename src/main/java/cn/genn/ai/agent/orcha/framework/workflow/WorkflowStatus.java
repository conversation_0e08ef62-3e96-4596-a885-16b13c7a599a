package cn.genn.ai.agent.orcha.framework.workflow;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 工作流状态
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
public class WorkflowStatus {

    /**
     * 执行ID
     */
    private String executionId;

    /**
     * 工作流ID
     */
    private String workflowId;

    /**
     * 执行状态
     */
    private String status;

    /**
     * 执行进度
     */
    private Double progress;

    /**
     * 是否完成
     */
    private Boolean finished = false;

    /**
     * 错误信息
     */
    private String error;

    /**
     * 扩展数据
     */
    private Map<String, Object> metadata;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
