package cn.genn.ai.agent.orcha.framework.persistence.entity;

import cn.genn.ai.agent.orcha.api.dto.FileInfo;
import cn.genn.ai.agent.orcha.framework.persistence.utils.message.MergedMessage;
import cn.genn.ai.agent.orcha.shared.enums.TaskStatus;
import cn.genn.core.model.enums.DeletedTypeEnum;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 聊天会话的具体消息记录表
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
@TableName(value = "agent_chat_message", autoResultMap = true)
public class AgentChatMessage {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 智能体id
     */
    @TableField("app_id")
    private String appId;

    /**
     * 关联的会话ID
     */
    @TableField("chat_id")
    private String chatId;

    /**
     * 当前消息对应的执行任务ID
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 原始用户输入文本
     */
    @TableField("user_input")
    private String userInput;

    /**
     * 文件信息
     */
    @TableField(value = "file_info", typeHandler = JacksonTypeHandler.class)
    private List<FileInfo> fileInfo;

    /**
     * 聚合后的最终消息体
     */
    @TableField(value = "message_content", typeHandler = JacksonTypeHandler.class)
    private List<MergedMessage> messageContent;

    /**
     * 任务状态
     */
    @TableField("task_status")
    private TaskStatus taskStatus;

    /**
     * redis最后一条消息的id
     */
    @TableField("last_message_id")
    private String lastMessageId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建者名称
     */
    @TableField(value = "create_user_name", fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 修改人名称
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 逻辑删除（0：未删除  1：删除）
     */
    @TableField("deleted")
    private DeletedTypeEnum deleted;
}
