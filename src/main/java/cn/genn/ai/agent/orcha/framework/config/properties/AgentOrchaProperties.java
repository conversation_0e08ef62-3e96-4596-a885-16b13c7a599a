package cn.genn.ai.agent.orcha.framework.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

/**
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "genn.ai.agent.orcha")
public class AgentOrchaProperties {

    @NestedConfigurationProperty
    private CerebroWorkflowProperties cerebro  = new CerebroWorkflowProperties();
}
