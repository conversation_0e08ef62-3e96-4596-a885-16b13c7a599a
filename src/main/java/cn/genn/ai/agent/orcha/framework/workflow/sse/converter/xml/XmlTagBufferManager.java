package cn.genn.ai.agent.orcha.framework.workflow.sse.converter.xml;

import cn.genn.ai.agent.orcha.shared.constants.CacheConstants;
import cn.hutool.core.util.ObjUtil;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * 全局缓冲区管理器
 * 使用Caffeine缓存管理各种类型的缓冲区，支持按任务ID索引
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class XmlTagBufferManager {

    /**
     * 全局缓冲区缓存
     */
    private static final Cache<String, XmlTagBuffer> BUFFER_CACHE = Caffeine.newBuilder()
        .maximumSize(10000) // 最大缓存10000个key
        .expireAfterWrite(Duration.ofSeconds(CacheConstants.AGENT_STREAM_TTL)) // 与Redis过期时间一致
        .build();

    private static final Cache<String, Boolean> WORKSPACE_AREA_SIGN_CACHE = Caffeine.newBuilder()
        .maximumSize(10000) // 最大缓存10000个key
        .expireAfterWrite(Duration.ofSeconds(CacheConstants.AGENT_STREAM_TTL)) // 与Redis过期时间一致
        .build();


    public static Boolean getWorkspaceAreaSign(String taskId) {
        return WORKSPACE_AREA_SIGN_CACHE.get(taskId,a-> false);
    }

    public static void setWorkspaceAreaSign(String taskId, Boolean sign) {
        WORKSPACE_AREA_SIGN_CACHE.put(taskId, sign);
    }

    /**
     * 获取指定任务的XML标签缓冲区
     * 如果不存在则创建新的缓冲区
     *
     * @param taskId 任务ID
     * @return XML标签缓冲区
     */
    public static XmlTagBuffer getXmlTagBuffer(String taskId) {
        XmlTagBuffer buffer = BUFFER_CACHE.get(taskId, k -> new XmlTagBuffer(taskId));
        return buffer;
    }

    /**
     * 安全获取缓冲区（不自动创建）
     */
    public static XmlTagBuffer getBufferIfPresent(String taskId) {
        return BUFFER_CACHE.getIfPresent(taskId);
    }

    /**
     * 检查是否存在指定任务的缓冲区
     */
    public static boolean containsBuffer(String taskId) {
        return BUFFER_CACHE.asMap().containsKey(taskId);
    }

    /**
     * 移除指定任务的所有缓冲区
     *
     * @param taskId 任务ID
     */
    public static void removeAllBuffers(String taskId) {
        BUFFER_CACHE.invalidate(taskId);
    }

    /**
     * 获取当前缓存大小
     *
     * @return 缓存大小
     */
    public static long getCacheSize() {
        return BUFFER_CACHE.estimatedSize();
    }

    /**
     * 清理所有缓存
     */
    @PreDestroy
    public static void cleanAllCache() {
        BUFFER_CACHE.invalidateAll();
        log.info("已清理所有缓冲区缓存");
    }
}
