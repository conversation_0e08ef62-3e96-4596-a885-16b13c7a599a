package cn.genn.ai.agent.orcha.framework.config;

import cn.genn.ai.agent.orcha.framework.config.properties.AgentOrchaProperties;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.web.client.RestClientBuilderConfigurer;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.client.RestClient;

/**
 * <AUTHOR>
 */
@Configuration
public class AgentOrchaConfig {

    /**
     * 注入一个默认的 RestClient.Builder,防止覆盖底层的 RestClient.Builder
     */
    @Bean
    @Primary
    RestClient.Builder restClientBuilder(RestClientBuilderConfigurer configurer) {
        return configurer.configure(RestClient.builder());
    }

    @Bean("lbRestClientBuilder")
    @LoadBalanced
    RestClient.Builder lbRestClientBuilder(RestClientBuilderConfigurer configurer) {
        return configurer.configure(RestClient.builder());
    }

    @Bean
    public RestClient aiHubRestClient(@Qualifier("lbRestClientBuilder") RestClient.Builder lbBuilder, AgentOrchaProperties properties) {
        return lbBuilder.baseUrl(properties.getCerebro().getGennAIHubUrl())
            .build();
    }
}
