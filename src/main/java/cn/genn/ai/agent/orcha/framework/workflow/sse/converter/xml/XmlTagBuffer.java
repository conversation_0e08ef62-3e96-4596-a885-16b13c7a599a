package cn.genn.ai.agent.orcha.framework.workflow.sse.converter.xml;


import cn.genn.ai.agent.orcha.api.dto.AgentMessage;
import cn.genn.ai.agent.orcha.shared.enums.AreaType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayDeque;
import java.util.Deque;


/**
 * XML标签缓冲区管理器
 */
@Data
@Slf4j
public class XmlTagBuffer {

    // 内容缓冲区
    private String buffer = "";

    private final String taskId;
    //进度条
    private Double progress = 0.0;

    // 标签栈
    private final Deque<TagState> tagStack = new ArrayDeque<>();

    /**
     * -- SETTER --
     *  设置待发送消息
     */
    // 待发送的消息（用于等待完整内容的标签）
    private AgentMessage pendingMessage;

    /**
     * 获取并清空待发送消息
     */
    public AgentMessage getPendingMessage() {
        AgentMessage message = this.pendingMessage;
        this.pendingMessage = null;
        return message;
    }

    /**
     * 检查是否有待发送消息
     */
    public boolean hasPendingMessage() {
        return this.pendingMessage != null;
    }

    /**
     * 添加内容到缓冲区
     */
    public void appendContent(String content) {
        buffer += content;
    }

    /**
     * 清空缓冲区
     */
    public void clearBuffer() {
        buffer = "";
    }

    /**
     * 获取当前标签
     */
    public TagState getCurrentTag() {
        return tagStack.isEmpty() ? null : tagStack.peek();
    }

    /**
     * 获取父标签
     */
    public TagState getParentTag() {
        if (tagStack.size() < 2) {
            return null;
        }
        TagState[] elements = tagStack.toArray(new TagState[0]);
        return elements[1];
    }

    /**
     * 压入标签
     */
    public void pushTag(TagState tagState) {
        tagStack.push(tagState);
    }

    /**
     * 弹出标签
     */
    public void popTag() {
        if (!tagStack.isEmpty()) {
            tagStack.pop();
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TagState {

        @Schema(description = "标签类型")
        private XmlTagType tagType;

        @Schema(description = "区域类型标识")
        private AreaType area;

        @Schema(description = "消息片段ID")
        private String cardId;

        @Schema(description = "是否等待完整内容")
        private boolean waitForComplete;

        @Schema(description = "累积的内容（用于等待完整内容的标签）")
        private StringBuilder accumulatedContent;

        /**
         * 添加内容到累积缓冲区
         */
        public void appendContent(String content) {
            if (accumulatedContent == null) {
                accumulatedContent = new StringBuilder();
            }
            accumulatedContent.append(content);
        }

        /**
         * 获取累积的内容
         */
        public String getAccumulatedContent() {
            return accumulatedContent != null ? accumulatedContent.toString() : "";
        }

        /**
         * 清空累积内容
         */
        public void clearAccumulatedContent() {
            if (accumulatedContent != null) {
                accumulatedContent.setLength(0);
            }
        }
    }



}
