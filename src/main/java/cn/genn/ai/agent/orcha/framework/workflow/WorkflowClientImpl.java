package cn.genn.ai.agent.orcha.framework.workflow;

import cn.genn.ai.agent.orcha.framework.cache.CancellationSignalManager;
import cn.genn.ai.agent.orcha.framework.config.properties.AgentOrchaProperties;
import cn.genn.ai.agent.orcha.framework.workflow.dto.WorkflowRequest;
import cn.genn.ai.agent.orcha.framework.workflow.dto.WorkflowResponse;
import cn.genn.core.utils.jackson.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import java.time.Duration;

/**
 * Cerebro工作流客户端实现
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WorkflowClientImpl implements WorkflowClient {

    private final AgentOrchaProperties agentOrchaProperties;
    private static final String CHAT_COMPLETIONS_PATH = "/web-api/v1/chat/completions";
    private static final String CHAT_CANCEL_PATH = "/web-api/workflow/tasks/cancel";
    private final CancellationSignalManager cancellationSignalManager; // 注入取消信号管理器


    private final WebClient webClient;

    @Override
    public Flux<WorkflowResponse> executeStreamRaw(WorkflowRequest request) {
        return webClient
            .post()
            .uri(agentOrchaProperties.getCerebro().getWorkflowUrl() + CHAT_COMPLETIONS_PATH)
            .header("token", request.getUserInfo().getToken())
            .header(HttpHeaders.COOKIE, request.getHttpHeaders().get("cookie"))
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(JsonUtils.toJsonNotNull(request))
            .exchangeToFlux(response -> {
                //所有响应头都打一遍
                response.headers().asHttpHeaders().forEach((key, values) -> {
                    log.info("响应头: {} = {}", key, values);
                });
                // 尝试从响应头中获取job-id
                String taskId = response.headers().asHttpHeaders().getFirst("task-id");
                if (taskId == null) {
                    log.error("响应头中未找到taskId");
                    return Flux.error(new RuntimeException("响应错误"));
                }
                WorkflowResponse headerResponse = new WorkflowResponse()
                    .setAppId(request.getAppId())
                    .setChatId(request.getChatId())
                    .setTaskId(taskId);

                // 然后发出包含SSE数据的WorkflowResponse流
                Flux<WorkflowResponse> sseResponseFlux = response
                    .bodyToFlux(new ParameterizedTypeReference<ServerSentEvent<String>>() {
                    })
                    .map(sseEvent -> {
                        log.info("接收到SSE事件: {}", sseEvent);
                        return new WorkflowResponse()
                            .setAppId(request.getAppId())
                            .setChatId(request.getChatId())
                            .setTaskId(taskId)
                            .setContent(sseEvent);
                    })
                    // 关键：添加取消信号监听
                    .takeUntil(ignored -> cancellationSignalManager.isCancelled(taskId))
                    .doOnCancel(() -> log.info("WebClient请求已被取消，taskId: {}", taskId));

                return Flux.concat(Flux.just(headerResponse), sseResponseFlux);
            })
            .timeout(Duration.ofSeconds(request.getTimeout()))
            .doOnSubscribe(subscription -> {
                log.info("开始执行Cerebro工作流，appId: {}, chatId: {}", request.getAppId(), request.getChatId());
            })
            .doOnError(error -> {
                log.error("Cerebro工作流执行出错，appId: {}, chatId: {}", request.getAppId(), request.getChatId(), error);
            })
            .doOnComplete(() -> {
                log.info("Cerebro工作流执行完成，appId: {}, chatId: {}", request.getAppId(), request.getChatId());
            });
    }

    @Override
    public boolean stopExecution(String appId, String taskId) {
        try {
            // 构建取消请求的JSON参数
            String cancelRequestBody = String.format("{\"taskId\":\"%s\",\"appId\":\"%s\"}", taskId, appId);
            // 发送取消请求
            String response = webClient
                .post()
                .uri(agentOrchaProperties.getCerebro().getWorkflowUrl() + CHAT_CANCEL_PATH)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + agentOrchaProperties.getCerebro().getWorkflowAuthKey())
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(cancelRequestBody)
                .retrieve()
                .bodyToMono(String.class)
                .block();

            log.info("工作流取消请求成功，taskId: {}, response: {}", taskId, response);
            return true;

        } catch (Exception e) {
            log.error("停止工作流执行失败，taskId: {}", taskId, e);
            return false;
        }
    }
}
