package cn.genn.ai.agent.orcha.shared.constants;

/**
 * 缓存常量定义
 * <p>
 * 统一管理Redis缓存相关的键名、过期时间等常量
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class CacheConstants {

    public static final String CACHE_KEY_PREFIX = "GENN:AI:ORCHA:"; // 全局缓存键前缀

    private CacheConstants() {
        // 工具类，禁止实例化
    }

    // ==================== 缓存键前缀 ====================

    /**
     * Agent消息流键前缀
     * 格式: stream:{taskId}
     */
    public static final String AGENT_STREAM_KEY_PREFIX = CACHE_KEY_PREFIX + "AGENT:STREAM";

    /**
     * Agent任务状态键前缀
     * 格式: agent:task:{taskId}
     */
    public static final String AGENT_TASK_STATUS_KEY_PREFIX = CACHE_KEY_PREFIX + "AGENT:TASK";

    /**
     * Agent: 每个chatId关联的运行中的taskId
     * 格式: agent:chat:task:{chatId}
     */
    public static final String AGENT_CHAT_TASK_RELATION_KEY_PREFIX = CACHE_KEY_PREFIX + "AGENT:CHAT:TASK:";

    /**
     * Agent任务取消键前缀
     */
    public static final String AGENT_TASK_CANCELLATION_KEY_PREFIX = CACHE_KEY_PREFIX + "AGENT:CANCELLATION:";


    // ==================== 过期时间配置 ====================

    /**
     * Agent消息流过期时间（秒）
     * 默认2小时
     */
    public static final long AGENT_STREAM_TTL = 2 * 60 * 60L;

    /**
     * Agent任务状态过期时间（秒）
     * 默认7天
     */
    public static final long AGENT_TASK_STATUS_TTL = 7 * 24 * 60 * 60L;

    /**
     * Agent运行中的任务ID列表过期时间（秒）
     * 默认60分钟
     */
    public static final long AGENT_RUNNING_TASK_IDS_TTL = 60 * 60L;

    // ==================== 缓存配置参数 ====================

    /**
     * Redis Stream裁剪保留长度
     * 裁剪时保留的消息数量
     */
    public static final long TRIM_STREAM_SIZE = 5000L;


    // ==================== 工具方法 ====================

    /**
     * 构建Agent任务状态键
     *
     * @param taskId 任务ID
     * @return 缓存键
     */
    public static String buildAgentTaskStatusKey(String taskId) {
        return AGENT_TASK_STATUS_KEY_PREFIX + ":" + taskId;
    }

    /**
     * 构建Agent消息流键
     *
     * @param taskId 任务ID
     * @return 缓存键
     */
    public static String buildAgentStreamKey(String taskId) {
        return AGENT_STREAM_KEY_PREFIX + ":" + taskId;
    }

    /**
     * 构建Agent与Chat任务关联的键
     *
     * @param chatId 会话ID
     * @return 缓存键
     */
    public static String buildAgentRunningTaskIdsKey(String chatId) {
        return AGENT_CHAT_TASK_RELATION_KEY_PREFIX + chatId;
    }
}
