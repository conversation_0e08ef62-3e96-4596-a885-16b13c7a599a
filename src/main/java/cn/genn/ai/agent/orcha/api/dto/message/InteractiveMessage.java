package cn.genn.ai.agent.orcha.api.dto.message;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * 交互式消息
 *
 * 用于处理用户交互，如选择、输入等
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "交互式消息")
public class InteractiveMessage {

    @Schema(description = "交互类型", example = "userSelect")
    private String type;

    @Schema(description = "交互参数")
    private Map<String, Object> params;

    @Schema(description = "入口节点ID列表")
    private List<String> entryNodeIds;

    @Schema(description = "内存边缘信息")
    private List<Map<String, Object>> memoryEdges;

    @Schema(description = "节点输出信息")
    private List<Map<String, Object>> nodeOutputs;

    @Schema(description = "交互状态", example = "waiting")
    private String status = "waiting";

    @Schema(description = "是否完成", example = "false")
    private Boolean finished = false;

    /**
     * 用户选择选项
     */
    @Data
    @Accessors(chain = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "用户选择选项")
    public static class UserSelectOption {
        @Schema(description = "选项值", example = "Confirm")
        private String value;

        @Schema(description = "选项键", example = "option1")
        private String key;
    }

    /**
     * 用户输入表单字段
     */
    @Data
    @Accessors(chain = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "用户输入表单字段")
    public static class InputFormField {
        @Schema(description = "字段类型", example = "input")
        private String type;

        @Schema(description = "字段键", example = "input1")
        private String key;

        @Schema(description = "字段标签", example = "请输入内容")
        private String label;

        @Schema(description = "字段描述")
        private String description;

        @Schema(description = "字段值")
        private String value;

        @Schema(description = "最大长度")
        private Integer maxLength;

        @Schema(description = "默认值")
        private String defaultValue;

        @Schema(description = "值类型", example = "string")
        private String valueType;

        @Schema(description = "是否必填", example = "true")
        private Boolean required;

        @Schema(description = "选项列表")
        private List<Map<String, String>> list;
    }
}
