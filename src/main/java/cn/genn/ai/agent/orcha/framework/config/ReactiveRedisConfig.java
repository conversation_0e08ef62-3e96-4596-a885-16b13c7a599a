package cn.genn.ai.agent.orcha.framework.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.ReactiveRedisConnectionFactory;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * Redis 响应式配置
 *
 * 为 Pub/Sub 功能提供必需的 ReactiveRedisTemplate Bean
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class ReactiveRedisConfig {

    @Bean
    public ReactiveRedisTemplate<String, String> reactiveRedisTemplate(ReactiveRedisConnectionFactory factory) {
        // 我们需要一个处理字符串的模板，因为我们将在频道中发布JSON字符串
        StringRedisSerializer serializer = new StringRedisSerializer();

        RedisSerializationContext<String, String> context = RedisSerializationContext
                .<String, String>newSerializationContext(serializer)
                .hashKey(serializer)
                .hashValue(serializer)
                .build();
                
        return new ReactiveRedisTemplate<>(factory, context);
    }
}