package cn.genn.ai.agent.orcha.framework.workflow.sse.converter;

import cn.genn.ai.agent.orcha.api.dto.AgentMessage;
import cn.genn.ai.agent.orcha.api.dto.ExecutionRequest;
import cn.genn.ai.agent.orcha.framework.workflow.sse.CerebroEventType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 流节点事件转换器
 *
 * 处理flowNodeResponse和flowNodeStatus事件
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class FlowNodeConverter extends BaseEventConverter {

    @Override
    public boolean supports(String eventType) {
        return CerebroEventType.FLOW_NODE_RESPONSE.getCode().equals(eventType)
            || CerebroEventType.FLOW_NODE_STATUS.getCode().equals(eventType);
    }

    @Override
    public List<AgentMessage> convert(ServerSentEvent<String> event, ExecutionRequest request, String taskId) {
        //暂不处理该类型事件
        return null;
    }

    @Override
    public int getOrder() {
        return 40;
    }
}
