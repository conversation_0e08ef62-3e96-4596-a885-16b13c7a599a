package cn.genn.ai.agent.orcha.api.dto.message;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 链接处理
 */
@Slf4j
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UrlMessage {

    @Schema(description = "链接地址")
    private String url;

    @Schema(description = "链接标题")
    private String title;

    @Schema(description = "链接图标")
    private String icon;

    @Schema(description = "链接摘要")
    private String summary;

    @Data
    public static class Fetch {

        private String url;
        private String name;
        private String sitelcon;
        private String summary;
        private String content;
    }

    public static List<Object> handleFetch(String data) {
        if(StrUtil.isBlank(data) || data.equals("[null]")){
            return null;
        }
        List<Fetch> fetches = JsonUtils.parseToList(data, Fetch.class);
        if (CollUtil.isEmpty(fetches)) {
            log.warn("<fetch>标签内容处理异常: {}", data);
            return null;
        }
        return fetches.stream()
                .filter(java.util.Objects::nonNull)
                .map(fetch -> UrlMessage.builder()
                        .url(fetch.url)
                        .title(fetch.name)
                        .icon(fetch.sitelcon)
                        .summary(fetch.summary).build()
                ).collect(Collectors.toList());
    }


}
