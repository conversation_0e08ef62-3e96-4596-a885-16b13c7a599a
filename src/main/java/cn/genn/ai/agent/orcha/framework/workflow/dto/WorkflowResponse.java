package cn.genn.ai.agent.orcha.framework.workflow.dto;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.http.codec.ServerSentEvent;

/**
 * 工作流响应
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
public class WorkflowResponse {

    /**
     * 工作流ID
     */
    private String appId;

    /**
     * 聊天ID
     */
    private String chatId;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 响应sse报文
     */
    private ServerSentEvent<String> content;

}
