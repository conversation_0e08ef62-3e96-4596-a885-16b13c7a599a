package cn.genn.ai.agent.orcha.framework.workflow.sse;

import cn.genn.ai.agent.orcha.api.dto.AgentMessage;
import cn.genn.ai.agent.orcha.api.dto.ExecutionRequest;
import cn.hutool.core.collection.CollUtil;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.CancellationException;

/**
 * 模块化SSE事件处理器
 *
 * 基于Spring Boot内置SSE支持和模块化转换器的新架构
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ModularSseEventProcessor {

    private final EventConverterManager converterManager;
//    private final ToolEventConverter toolEventConverter;

    /**
     * 处理SSE原始数据流
     *
     * @param rawDataFlux 原始SSE数据流
     * @param chatId 聊天ID
     * @param taskId 任务ID
     * @return AgentMessage流
     */
    public Flux<List<AgentMessage>> processEventStream(Flux<ServerSentEvent<String>> rawDataFlux, ExecutionRequest request, String chatId, String taskId) {
        return rawDataFlux
                .doOnSubscribe(subscription -> log.info("开始处理SSE事件流，chatId: {}, taskId: {}", chatId, taskId))
                // 转换为AgentMessage
                .flatMap(event -> convertEvent(event, request, chatId, taskId))
                // 过滤空消息
                .filter(Objects::nonNull)
                .doOnComplete(() -> {
                    log.info("SSE事件流处理完成，taskId: {}, dataId: {}", chatId, taskId);
                    cleanupResources(taskId);
                })
                .doOnError(error -> {
                    // 判断是否为取消异常
                    if (error instanceof CancellationException) {
                        log.info("SSE事件流被客户端取消，任务ID: {}, dataId: {}", chatId, taskId);
                    } else {
                        log.error("SSE事件流处理失败，taskId: {}, dataId: {}", chatId, taskId, error);
                    }
                    cleanupResources(taskId);
                })
                .doOnCancel(() -> {
                    log.info("SSE事件流被取消，taskId: {}, dataId: {}", chatId, taskId);
                    cleanupResources(taskId);
                });
    }

    /**
     * 转换单个事件
     */
    private Flux<List<AgentMessage>> convertEvent(ServerSentEvent<String> event, ExecutionRequest request, String chatId, String taskId) {
        try {
            List<AgentMessage> messages = converterManager.convert(event, request, taskId);
            if(CollUtil.isEmpty(messages)){
                return Flux.empty();
            }
            messages.forEach(message -> {
                message.setChatId(chatId);
            });
            return Flux.just(messages);
        } catch (Exception e) {
            log.error("转换事件失败，eventType: {}", event.event(), e);
            return Flux.empty();
        }
    }

    /**
     * 清理资源
     */
    private void cleanupResources(String taskId) {
        try {
//            toolEventConverter.clearTaskCache(taskId);  //工具调用同步注释掉
            log.debug("清理任务 {} 的相关资源", taskId);
        } catch (Exception e) {
            log.error("清理任务 {} 资源时发生异常", taskId, e);
        }
    }

    /**
     * 获取处理器状态信息
     */
    public ProcessorStatus getStatus() {
        return new ProcessorStatus()
                .setConverterCount(converterManager.getConverterCount())
                .setSupportedEventTypes(converterManager.getSupportedEventTypes());
    }

    /**
     * 强制清理指定任务的资源
     */
    public void forceCleanupTask(String taskId) {
        cleanupResources(taskId);
        log.info("强制清理任务资源: {}", taskId);
    }

    /**
     * 处理器状态信息
     */
    @Data
    @Accessors(chain = true)
    public static class ProcessorStatus {
        private int converterCount;
        private java.util.List<String> supportedEventTypes;

    }
}
