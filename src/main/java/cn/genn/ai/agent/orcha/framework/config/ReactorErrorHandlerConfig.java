package cn.genn.ai.agent.orcha.framework.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import reactor.core.publisher.Hooks;

import jakarta.annotation.PostConstruct;

/**
 * Reactor全局错误处理配置
 * 
 * 用于处理Redisson等第三方库产生的无害错误，避免污染日志
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Configuration
public class ReactorErrorHandlerConfig {

    @PostConstruct
    public void configureReactorErrorHandling() {
        // 设置全局错误丢弃处理器
        Hooks.onErrorDropped(throwable -> {
            // 忽略Redisson取消订阅时的UnsupportedOperationException
            if (throwable instanceof UnsupportedOperationException) {
                log.debug("忽略Redisson取消订阅错误: {}", throwable.getMessage());
                return;
            }
            
            // 检查是否是Redisson相关的错误
            if (throwable.getCause() instanceof UnsupportedOperationException) {
                log.debug("忽略Redisson相关错误: {}", throwable.getMessage());
                return;
            }
            
            // 检查堆栈跟踪中是否包含Redisson相关类
            String stackTrace = throwable.toString();
            if (stackTrace.contains("RedissonReactiveSubscription") || 
                stackTrace.contains("Collections$SingletonList.removeIf")) {
                log.debug("忽略Redisson内部错误: {}", throwable.getMessage());
                return;
            }
            
            // 其他错误正常记录
            log.error("未处理的Reactor错误", throwable);
        });
        
        log.info("Reactor全局错误处理器已配置");
    }
}