package cn.genn.ai.agent.orcha.framework.workflow.sse.converter.tag;

import cn.genn.ai.agent.orcha.api.dto.AgentMessage;
import cn.genn.ai.agent.orcha.api.dto.ExecutionRequest;
import cn.genn.ai.agent.orcha.framework.workflow.sse.converter.xml.XmlTagBuffer;
import cn.genn.ai.agent.orcha.framework.workflow.sse.converter.xml.XmlTagBufferManager;
import cn.genn.ai.agent.orcha.framework.workflow.sse.converter.xml.XmlTagParser;
import cn.genn.ai.agent.orcha.shared.enums.AreaType;
import cn.genn.ai.agent.orcha.shared.enums.CardType;
import cn.genn.ai.agent.orcha.shared.enums.ChatMode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class XmlTagParserTest {

    @InjectMocks
    private XmlTagParser xmlTagParser;

    private ExecutionRequest mockRequest;
    private String taskId = "test-task-id";

    @BeforeEach
    void setUp() {
        mockRequest = mock(ExecutionRequest.class);
        when(mockRequest.getChatMode()).thenReturn(ChatMode.COMMON);
        // 清理缓存
        XmlTagBufferManager.cleanAllCache();
    }

    @Test
    void testPureText() {
        // 测试纯文本情况
        String text = "Hello world, this is pure text.";

        List<AgentMessage> messages = xmlTagParser.parse(text, taskId, mockRequest);

        assertEquals(1, messages.size());
        AgentMessage message = messages.get(0);
        assertEquals(CardType.ASSISTANT, message.getCardType());
        assertEquals(AreaType.CONTENT, message.getArea());
        assertEquals(1, message.getStatus());
        assertNull(message.getParentCardId());
        assertTrue(message.getMessages().get(0).toString().contains("Hello world"));
    }

    @Test
    void testSimpleTagPair() {
        // 测试简单标签对
        String text = "<thinking>I need to think about this problem</thinking>";

        List<AgentMessage> messages = xmlTagParser.parse(text, taskId, mockRequest);

        assertEquals(3, messages.size());

        // 开始标签
        AgentMessage startMessage = messages.get(0);
        assertEquals(CardType.THINKING, startMessage.getCardType());
        assertEquals(AreaType.THINK, startMessage.getArea());
        assertEquals(1, startMessage.getStatus());
        assertNull(startMessage.getParentCardId());

        // 内容
        AgentMessage contentMessage = messages.get(1);
        assertEquals(CardType.THINKING, contentMessage.getCardType());
        assertEquals(AreaType.THINK, contentMessage.getArea());
        assertEquals(1, contentMessage.getStatus());
        assertEquals(startMessage.getCardId(), contentMessage.getParentCardId());

        // 结束标签
        AgentMessage endMessage = messages.get(2);
        assertEquals(CardType.THINKING, endMessage.getCardType());
        assertEquals(AreaType.THINK, endMessage.getArea());
        assertEquals(0, endMessage.getStatus());
        assertEquals(startMessage.getCardId(), endMessage.getCardId());
        assertNull(endMessage.getParentCardId());
    }

    @Test
    void testNestedTags() {
        // 测试嵌套标签
        String text = "<thinking>Outer thought<step>Inner step</step>Continue thinking</thinking>";

        List<AgentMessage> messages = xmlTagParser.parse(text, taskId, mockRequest);

        assertEquals(7, messages.size());

        // thinking开始
        assertEquals(CardType.THINKING, messages.get(0).getCardType());
        assertEquals(1, messages.get(0).getStatus());

        // 外层文本
        assertEquals(CardType.THINKING, messages.get(1).getCardType());
        assertEquals(messages.get(0).getCardId(), messages.get(1).getParentCardId());

        // step开始
        assertEquals(CardType.STEP, messages.get(2).getCardType());
        assertEquals(1, messages.get(2).getStatus());
        assertEquals(messages.get(0).getCardId(), messages.get(2).getParentCardId());

        // step内容
        assertEquals(CardType.STEP, messages.get(3).getCardType());
        assertEquals(messages.get(2).getCardId(), messages.get(3).getParentCardId());

        // step结束
        assertEquals(CardType.STEP, messages.get(4).getCardType());
        assertEquals(0, messages.get(4).getStatus());
        assertEquals(messages.get(2).getCardId(), messages.get(4).getCardId());

        // 继续外层文本
        assertEquals(CardType.THINKING, messages.get(5).getCardType());
        assertEquals(messages.get(0).getCardId(), messages.get(5).getParentCardId());

        // thinking结束
        assertEquals(CardType.THINKING, messages.get(6).getCardType());
        assertEquals(0, messages.get(6).getStatus());
        assertEquals(messages.get(0).getCardId(), messages.get(6).getCardId());
    }

    @Test
    void testEmptyTagElimination() {
        // 测试消消乐 - 空标签对
        String text = "<thinking></thinking>Normal text after.";

        List<AgentMessage> messages = xmlTagParser.parse(text, taskId, mockRequest);

        assertEquals(1, messages.size());
        AgentMessage message = messages.get(0);
        assertEquals(CardType.ASSISTANT, message.getCardType());
        assertTrue(message.getMessages().get(0).toString().contains("Normal text after"));
    }

    @Test
    void testEmptyTagWithWhitespaceElimination() {
        // 测试消消乐 - 只有空白的标签对
        String text = "<thinking>   </thinking>Normal text after.";

        List<AgentMessage> messages = xmlTagParser.parse(text, taskId, mockRequest);

        assertEquals(1, messages.size());
        AgentMessage message = messages.get(0);
        assertEquals(CardType.ASSISTANT, message.getCardType());
        assertTrue(message.getMessages().get(0).toString().contains("Normal text after"));
    }

    @Test
    void testIncompleteTag() {
        // 测试不完整标签 - 应该等待下一个chunk
        String text = "Some text <think";

        List<AgentMessage> messages = xmlTagParser.parse(text, taskId, mockRequest);

        assertEquals(1, messages.size());
        AgentMessage message = messages.get(0);
        assertEquals(CardType.ASSISTANT, message.getCardType());
        assertTrue(message.getMessages().get(0).toString().contains("Some text"));

        // 验证缓冲区还有不完整的标签
        XmlTagBuffer buffer = XmlTagBufferManager.getBufferIfPresent(taskId);
        assertNotNull(buffer);
        assertEquals("<think", buffer.getBuffer());
    }

    @Test
    void testStreamingProcessing() {
        // 测试流式处理 - 分多个chunk
        String chunk1 = "<thinking>Part one";
        String chunk2 = " and part two</thinking>";

        // 处理第一个chunk
        List<AgentMessage> messages1 = xmlTagParser.parse(chunk1, taskId, mockRequest);
        assertEquals(2, messages1.size());
        assertEquals(CardType.THINKING, messages1.get(0).getCardType());
        assertEquals(1, messages1.get(0).getStatus()); // 开始标签
        assertEquals(CardType.THINKING, messages1.get(1).getCardType());
        assertTrue(messages1.get(1).getMessages().get(0).toString().contains("Part one"));

        // 处理第二个chunk
        List<AgentMessage> messages2 = xmlTagParser.parse(chunk2, taskId, mockRequest);
        assertEquals(2, messages2.size());
        assertEquals(CardType.THINKING, messages2.get(0).getCardType());
        assertTrue(messages2.get(0).getMessages().get(0).toString().contains("and part two"));
        assertEquals(CardType.THINKING, messages2.get(1).getCardType());
        assertEquals(0, messages2.get(1).getStatus()); // 结束标签
    }

    @Test
    void testMixedContent() {
        // 测试混合内容
        String text = "Before tag<thinking>Think content</thinking>After tag";

        List<AgentMessage> messages = xmlTagParser.parse(text, taskId, mockRequest);

        assertEquals(5, messages.size());

        // 前面的文本
        assertEquals(CardType.ASSISTANT, messages.get(0).getCardType());
        assertTrue(messages.get(0).getMessages().get(0).toString().contains("Before tag"));

        // thinking开始
        assertEquals(CardType.THINKING, messages.get(1).getCardType());
        assertEquals(1, messages.get(1).getStatus());

        // thinking内容
        assertEquals(CardType.THINKING, messages.get(2).getCardType());
        assertTrue(messages.get(2).getMessages().get(0).toString().contains("Think content"));

        // thinking结束
        assertEquals(CardType.THINKING, messages.get(3).getCardType());
        assertEquals(0, messages.get(3).getStatus());

        // 后面的文本
        assertEquals(CardType.ASSISTANT, messages.get(4).getCardType());
        assertTrue(messages.get(4).getMessages().get(0).toString().contains("After tag"));
    }

    @Test
    void testWorkspaceArea() {
        // 测试工作空间区域
        when(mockRequest.getChatMode()).thenReturn(ChatMode.DEEP_RESEARCH);

        String text = "<progress><thinking><step>In workspace</step></thinking></progress>";

        List<AgentMessage> messages = xmlTagParser.parse(text, taskId, mockRequest);

//        assertEquals(3, messages.size());

        // 在工作空间内的消息应该有WORKSPACE区域
        assertEquals(AreaType.WORKSPACE, messages.get(0).getArea());
        assertEquals(AreaType.WORKSPACE, messages.get(1).getArea());
        assertEquals(AreaType.WORKSPACE, messages.get(2).getArea());
        assertEquals(AreaType.WORKSPACE, messages.get(3).getArea());
        assertEquals(AreaType.WORKSPACE, messages.get(4).getArea());
    }

    @Test
    void testMultipleTags() {
        // 测试多个连续标签
        String text = "<thinking>First thought</thinking><step>Next step</step>";

        List<AgentMessage> messages = xmlTagParser.parse(text, taskId, mockRequest);

        assertEquals(6, messages.size());

        // 第一个标签组
        assertEquals(CardType.THINKING, messages.get(0).getCardType());
        assertEquals(1, messages.get(0).getStatus());

        assertEquals(CardType.THINKING, messages.get(1).getCardType());
        assertTrue(messages.get(1).getMessages().get(0).toString().contains("First thought"));

        assertEquals(CardType.THINKING, messages.get(2).getCardType());
        assertEquals(0, messages.get(2).getStatus());

        // 第二个标签组
        assertEquals(CardType.STEP, messages.get(3).getCardType());
        assertEquals(1, messages.get(3).getStatus());

        assertEquals(CardType.STEP, messages.get(4).getCardType());
        assertTrue(messages.get(4).getMessages().get(0).toString().contains("Next step"));

        assertEquals(CardType.STEP, messages.get(5).getCardType());
        assertEquals(0, messages.get(5).getStatus());
    }

    @Test
    void testComplexNestedScenario() {
        // 测试复杂嵌套场景
        String text = "Start<thinking>Think1<step>Step1</step>Think2<summary>Sum1</summary>Think3</thinking>End";

        List<AgentMessage> messages = xmlTagParser.parse(text, taskId, mockRequest);

        assertEquals(13, messages.size());

        // 验证嵌套关系
        String thinkingCardId = messages.get(1).getCardId(); // thinking开始
        String stepCardId = messages.get(3).getCardId(); // step开始
        String summaryCardId = messages.get(7).getCardId(); // summary开始

        // 验证父子关系
        assertEquals(thinkingCardId, messages.get(3).getParentCardId()); // step的父是thinking
        assertEquals(stepCardId, messages.get(4).getParentCardId()); // step内容的父是step
        assertEquals(thinkingCardId, messages.get(7).getParentCardId()); // summary的父是thinking
        assertEquals(summaryCardId, messages.get(8).getParentCardId()); // summary内容的父是summary
    }

    @Test
    void testEmptyInput() {
        // 测试空输入
        String text = "";

        List<AgentMessage> messages = xmlTagParser.parse(text, taskId, mockRequest);

        assertEquals(0, messages.size());
    }

    @Test
    void testOnlyWhitespace() {
        // 测试只有空白字符
        String text = "   \n\t  ";

        List<AgentMessage> messages = xmlTagParser.parse(text, taskId, mockRequest);

        assertEquals(0, messages.size());
    }

    @Test
    void testUnsupportedTags() {
        // 测试不支持的标签
        String text = "<unsupported>This should be treated as text</unsupported>";

        List<AgentMessage> messages = xmlTagParser.parse(text, taskId, mockRequest);

        assertEquals(1, messages.size());
        assertEquals(CardType.ASSISTANT, messages.get(0).getCardType());
        assertTrue(messages.get(0).getMessages().get(0).toString().contains("<unsupported>"));
    }

    @Test
    void testIncompleteNestedTags() {
        // 测试不完整的嵌套标签
        String text = "<thinking>Start<step>Incomplete";

        List<AgentMessage> messages = xmlTagParser.parse(text, taskId, mockRequest);

        assertEquals(4, messages.size());

        // thinking开始
        assertEquals(CardType.THINKING, messages.get(0).getCardType());
        assertEquals(1, messages.get(0).getStatus());

        // thinking内容
        assertEquals(CardType.THINKING, messages.get(1).getCardType());
        assertTrue(messages.get(1).getMessages().get(0).toString().contains("Start"));

        // step开始
        assertEquals(CardType.STEP, messages.get(2).getCardType());
        assertEquals(1, messages.get(2).getStatus());
    }

    @Test
    void testProgressWithContent() {
        // 测试带内容的进度标签在不同模式下的行为
        when(mockRequest.getChatMode()).thenReturn(ChatMode.DEEP_RESEARCH);

        String text = "<progress>Loading data...</progress><thinking>Analysis</thinking>";

        List<AgentMessage> messages = xmlTagParser.parse(text, taskId, mockRequest);

        // PROGRESS标签不产生消息，thinking正常处理
        assertEquals(4, messages.size());

        // 所有消息都应该在工作空间区域（因为progress包围了整个内容）
        // 但由于progress结束后工作空间标记被重置，thinking可能不在工作空间
        assertEquals(CardType.THINKING, messages.get(0).getCardType());
    }
}

