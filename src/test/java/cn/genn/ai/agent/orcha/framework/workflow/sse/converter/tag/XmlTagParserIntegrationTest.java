package cn.genn.ai.agent.orcha.framework.workflow.sse.converter.tag;

import cn.genn.ai.agent.orcha.api.dto.AgentMessage;
import cn.genn.ai.agent.orcha.api.dto.ExecutionRequest;
import cn.genn.ai.agent.orcha.framework.workflow.sse.converter.xml.XmlTagBufferManager;
import cn.genn.ai.agent.orcha.framework.workflow.sse.converter.xml.XmlTagParser;
import cn.genn.ai.agent.orcha.shared.enums.AreaType;
import cn.genn.ai.agent.orcha.shared.enums.CardType;
import cn.genn.ai.agent.orcha.shared.enums.ChatMode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class XmlTagParserIntegrationTest {

    @InjectMocks
    private XmlTagParser xmlTagParser;

    private ExecutionRequest mockRequest;
    private String taskId = "integration-test-task";

    @BeforeEach
    void setUp() {
        mockRequest = mock(ExecutionRequest.class);
        when(mockRequest.getChatMode()).thenReturn(ChatMode.DEEP_RESEARCH);
        XmlTagBufferManager.cleanAllCache();
    }

    @Test
    void testRealWorldStreamingScenario() {
        // 模拟真实世界的流式场景
        String[] chunks = {
            "<progress>",
            "<thinking>I need to analyze this problem step by step.",
            "\n\nFirst, let me understand the requirements:",
            "\n1. Parse XML tags",
            "\n2. Handle nested structures",
            "\n3. Support streaming</thinking>",
            "\n\n<step>Breaking down the problem:",
            "\n- Input validation",
            "\n- Parsing logic",
            "\n- Output formatting</step>",
            "\n\n<summary>The solution needs to handle both complete and incomplete XML tags in a streaming fashion.</summary>",
            "</progress>",
            "\n\nBased on my analysis, here's the approach I recommend..."
        };

        List<AgentMessage> allMessages = new ArrayList<>();

        for (String chunk : chunks) {
            List<AgentMessage> messages = xmlTagParser.parse(chunk, taskId, mockRequest);
            if (messages != null) {
                allMessages.addAll(messages);
            }
        }

        // 验证消息结构
        assertFalse(allMessages.isEmpty());

        // 验证工作空间区域
        boolean hasWorkspaceMessages = allMessages.stream()
            .anyMatch(msg -> msg.getArea() == AreaType.WORKSPACE);
        assertTrue(hasWorkspaceMessages, "Should have workspace area messages");

        // 验证嵌套关系
        long thinkingMessages = allMessages.stream()
            .filter(msg -> msg.getCardType() == CardType.THINKING)
            .count();
        assertTrue(thinkingMessages > 0, "Should have thinking messages");

        long stepMessages = allMessages.stream()
            .filter(msg -> msg.getCardType() == CardType.STEP)
            .count();
        assertTrue(stepMessages > 0, "Should have step messages");

        long summaryMessages = allMessages.stream()
            .filter(msg -> msg.getCardType() == CardType.SUMMARY)
            .count();
        assertTrue(summaryMessages > 0, "Should have summary messages");

        // 验证最后的普通文本
        AgentMessage lastMessage = allMessages.get(allMessages.size() - 1);
        assertEquals(CardType.ASSISTANT, lastMessage.getCardType());
        assertTrue(lastMessage.getMessages().get(0).toString().contains("Based on my analysis"));
    }

    @Test
    void testErrorRecoveryScenario() {
        // 测试错误恢复场景
        String[] chunks = {
            "<thinking>Normal start",
            "<broken>This is not a supported tag",
            " and should be treated as text</broken>",
            " continuing normal thinking</thinking>",
            "Normal text after."
        };

        List<AgentMessage> allMessages = new ArrayList<>();

        for (String chunk : chunks) {
            List<AgentMessage> messages = xmlTagParser.parse(chunk, taskId, mockRequest);
            if (messages != null) {
                allMessages.addAll(messages);
            }
        }

        // 验证处理结果
        assertFalse(allMessages.isEmpty());

        // 应该有thinking类型的消息
        boolean hasThinkingMessages = allMessages.stream()
            .anyMatch(msg -> msg.getCardType() == CardType.THINKING);
        assertTrue(hasThinkingMessages);

        // 不支持的标签应该被当作文本处理
        boolean hasUnsupportedTagAsText = allMessages.stream()
            .anyMatch(msg -> msg.getMessages().get(0).toString().contains("<broken>"));
        assertTrue(hasUnsupportedTagAsText);
    }

    @Test
    void testPerformanceWithLargeInput() {
        // 测试大输入的性能
        StringBuilder largeInput = new StringBuilder();
        largeInput.append("<thinking>");
        for (int i = 0; i < 1000; i++) {
            largeInput.append("This is line ").append(i).append(" of thinking content.\n");
        }
        largeInput.append("</thinking>");

        long startTime = System.currentTimeMillis();
        List<AgentMessage> messages = xmlTagParser.parse(largeInput.toString(), taskId, mockRequest);
        long endTime = System.currentTimeMillis();

        // 验证结果
        assertEquals(3, messages.size()); // 开始、内容、结束

        // 验证性能（应该在合理时间内完成）
        long duration = endTime - startTime;
        assertTrue(duration < 5000, "Processing should complete within 5 seconds, took: " + duration + "ms");
    }
}
